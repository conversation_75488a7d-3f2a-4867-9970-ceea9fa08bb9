{"name": "@vben/web-antd", "version": "5.5.7", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@codemirror/autocomplete": "catalog:", "@codemirror/lang-sql": "catalog:", "@codemirror/theme-one-dark": "catalog:", "@form-create/ant-design-vue": "catalog:", "@form-create/antd-designer": "catalog:", "@tinymce/tinymce-vue": "catalog:", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "ant-design-vue": "catalog:", "cropperjs": "catalog:", "crypto-js": "catalog:", "dayjs": "catalog:", "highlight.js": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-codemirror": "catalog:", "vue-dompurify-html": "catalog:", "vue-router": "catalog:", "vue3-signature": "catalog:", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/crypto-js": "catalog:"}}