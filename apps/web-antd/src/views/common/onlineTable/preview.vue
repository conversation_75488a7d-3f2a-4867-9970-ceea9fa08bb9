<template>
  <div class="app-container">
    <el-card shadow="never" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>{{ tableConfig?.tableComment || '在线表单预览' }}</span>
          <div class="header-actions">
            <el-button @click="handleExport" v-if="tableConfig?.page === 1">
              <Icon icon="ep:download" class="mr-5px" />
              导出
            </el-button>
            <el-button @click="goBack">
              <Icon icon="ep:back" class="mr-5px" />
              返回
            </el-button>
          </div>
        </div>
      </template>

      <!-- 查询条件 -->
      <el-card shadow="never" class="search-wrapper" v-if="queryFields.length > 0">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
          <el-form-item
            v-for="field in queryFields"
            :key="field.columnName"
            :label="field.columnText"
            :prop="field.columnName"
          >
            <!-- 输入框 -->
            <el-input
              v-if="field.componentType === 'input'"
              v-model="queryParams[field.columnName]"
              :placeholder="`请输入${field.columnText}`"
              clearable
              @keyup.enter="handleQuery"
              style="width: 180px"
            />
            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.componentType === 'input-number'"
              v-model="queryParams[field.columnName]"
              :placeholder="`请输入${field.columnText}`"
              style="width: 180px"
            />
            <!-- 选择器 -->
            <el-select
              v-else-if="field.componentType === 'select'"
              v-model="queryParams[field.columnName]"
              :placeholder="`请选择${field.columnText}`"
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="dict in getDictOptions(field.dictType)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="field.componentType === 'date-picker'"
              v-model="queryParams[field.columnName]"
              type="date"
              :placeholder="`请选择${field.columnText}`"
              style="width: 180px"
              value-format="YYYY-MM-DD"
            />
            <!-- 日期时间选择器 -->
            <el-date-picker
              v-else-if="field.componentType === 'datetime-picker'"
              v-model="queryParams[field.columnName]"
              type="datetime"
              :placeholder="`请选择${field.columnText}`"
              style="width: 200px"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <!-- 日期范围选择器 -->
            <el-date-picker
              v-else-if="field.componentType === 'daterange-picker'"
              v-model="queryParams[field.columnName]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" class="mr-5px" />
              重置
            </el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-if="!isReadOnlyTable"
            >
              <Icon icon="ep:plus" class="mr-5px" />
              新增
            </el-button>
            <el-button
              type="danger"
              plain
              @click="handleBatchDelete"
              v-if="!isReadOnlyTable && tableConfig?.checkbox === 1 && selectedRows.length > 0"
            >
              <Icon icon="ep:delete" class="mr-5px" />
              批量删除
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              v-if="!isReadOnlyTable"
            >
              <Icon icon="ep:download" class="mr-5px" />
              导出
            </el-button>
            <el-button
              type="info"
              plain
              @click="handleImport"
              v-if="!isReadOnlyTable"
            >
              <Icon icon="ep:upload" class="mr-5px" />
              导入
            </el-button>
            <el-button
              type="warning"
              plain
              @click="handleDownloadTemplate"
              v-if="!isReadOnlyTable"
            >
              <Icon icon="ep:document" class="mr-5px" />
              下载模板
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 数据表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        :show-header="true"
        :height="tableConfig?.scrollBar === 1 ? 400 : undefined"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        ref="tableRef"
      >
        <!-- 复选框列 -->
        <el-table-column
          v-if="tableConfig?.checkbox === 1"
          type="selection"
          width="55"
          align="center"
        />

        <!-- 序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center" />

        <!-- 数据列 -->
        <el-table-column
          v-for="field in listFields"
          :key="field.columnName"
          :prop="field.columnName"
          :label="field.columnText"
          :width="field.componentLength || undefined"
          :sortable="field.sortFlag ? 'custom' : false"
          :align="getColumnAlign(field)"
          show-overflow-tooltip
        >
          <template #default="scope">
            <!-- 字典类型显示 -->
            <dict-tag
              v-if="field.dictType"
              :type="field.dictType"
              :value="scope.row[field.columnName]"
            />
            <!-- 日期时间格式化显示 -->
            <span v-else-if="isDateTimeField(field)">
              {{ formatDateTime(scope.row[field.columnName], field.componentType) }}
            </span>
            <!-- 数字格式化显示 -->
            <span v-else-if="isNumberField(field)">
              {{ formatNumber(scope.row[field.columnName]) }}
            </span>
            <!-- 布尔值显示 -->
            <el-tag v-else-if="isBooleanField(field)" :type="scope.row[field.columnName] ? 'success' : 'info'">
              {{ scope.row[field.columnName] ? '是' : '否' }}
            </el-tag>
            <!-- 长文本显示 -->
            <el-tooltip v-else-if="isLongTextField(field)" :content="scope.row[field.columnName]" placement="top">
              <span>{{ truncateText(scope.row[field.columnName], 20) }}</span>
            </el-tooltip>
            <!-- 普通文本显示 -->
            <span v-else>{{ scope.row[field.columnName] }}</span>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column
          label="操作"
          align="center"
          width="180"
          fixed="right"
          v-if="!isReadOnlyTable"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleView(scope.row)"
              size="small"
            >
              查看
            </el-button>
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row)"
              size="small"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row)"
              size="small"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <Pagination
        v-if="tableConfig?.page === 1"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getTableData"
      />
    </el-card>

    <!-- 表单弹窗：添加/修改 -->
    <DynamicForm
      ref="dynamicFormRef"
      :table-config="tableConfig"
      @success="getTableData"
    />

    <!-- 查看详情弹窗 -->
    <DetailView
      ref="detailViewRef"
      :table-config="tableConfig"
      :is-read-only="isReadOnlyTable"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script lang="ts" setup>
import { getDictOptions } from '@/utils/dict'
import * as OnlineTableApi from '@/api/infra/onlineTable'
import { reactive, ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import DynamicForm from './components/DynamicForm.vue'
import DetailView from './components/DetailView.vue'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const tableLoading = ref(false)
const total = ref(0)
const selectedRows = ref<any[]>([])
const sortParams = ref<any>({})

const tableConfig = ref<OnlineTableApi.OnlineTableVO>()
const tableData = ref<any[]>([])
const queryFormRef = ref()
const tableRef = ref()
const dynamicFormRef = ref()
const detailViewRef = ref()

const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10
})

// 计算属性
const queryFields = computed(() => {
  return tableConfig.value?.pageConfigs?.filter(field => field.queryFlag)?.sort((a, b) => (a.sort || 0) - (b.sort || 0)) || []
})

const listFields = computed(() => {
  return tableConfig.value?.pageConfigs?.filter(field => field.listShowFlag)?.sort((a, b) => (a.sort || 0) - (b.sort || 0)) || []
})

// 是否为只读表格
const isReadOnlyTable = computed(() => {
  return tableConfig.value?.tableFormStyle === 2
})

// 获取主键字段
const primaryKeyField = computed(() => {
  return tableConfig.value?.columns?.find(col => col.privateKey)?.columnName || 'id'
})

/** 获取表单配置 */
const getTableConfig = async () => {
  const tableId = route.params.id as string
  if (!tableId) {
    ElMessage.error('表单ID不能为空')
    return
  }

  loading.value = true
  try {
    const res = await OnlineTableApi.getOnlineTable(tableId)
    tableConfig.value = res

    // 初始化查询参数
    queryFields.value.forEach(field => {
      queryParams[field.columnName] = field.componentDefaultValue || undefined
    })

    // 获取表格数据
    await getTableData()
  } finally {
    loading.value = false
  }
}

/** 获取表格数据 */
const getTableData = async () => {
  if (!tableConfig.value) return

  tableLoading.value = true
  try {
    // 构建查询条件
    const queryConditions: Record<string, any> = {}

    // 添加查询条件
    queryFields.value.forEach(field => {
      const value = queryParams[field.columnName]
      if (value !== undefined && value !== null && value !== '') {
        // 处理不同查询类型
        if (field.queryType === 2) { // 模糊查询
          queryConditions[field.columnName + '_like'] = value
        } else if (field.queryType === 3) { // 范围查询
          if (Array.isArray(value) && value.length === 2) {
            queryConditions[field.columnName + '_start'] = value[0]
            queryConditions[field.columnName + '_end'] = value[1]
          }
        } else { // 精确查询
          queryConditions[field.columnName] = value
        }
      }
    })

    // 添加排序参数
    if (sortParams.value.sortField) {
      queryConditions['sortField'] = sortParams.value.sortField
      queryConditions['sortOrder'] = sortParams.value.sortOrder
    }

    // 构建查询参数
    const params: OnlineTableApi.OnlineTableDataQueryDTO = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      tableId: tableConfig.value.tableId,
      queryParams: queryConditions
    }

    // 调用查询接口
    const res = await OnlineTableApi.queryTableData(params)
    tableData.value = res.list || []
    total.value = res.total || 0
  } catch (error) {
    console.error('获取表格数据失败:', error)
    ElMessage.error('获取表格数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    tableLoading.value = false
  }
}

/** 搜索 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getTableData()
}

/** 重置 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  // 重置查询参数为默认值
  queryFields.value.forEach(field => {
    queryParams[field.columnName] = field.componentDefaultValue || undefined
  })
  handleQuery()
}

/** 排序变化 */
const handleSortChange = ({ prop, order }: any) => {
  if (order) {
    sortParams.value = {
      sortField: prop,
      sortOrder: order === 'ascending' ? 'asc' : 'desc'
    }
  } else {
    sortParams.value = {}
  }
  handleQuery()
}

/** 选择变化 */
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

/** 批量删除 */
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm(`是否确认删除选中的 ${selectedRows.value.length} 条数据?`, '警告', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    // 逐个删除选中的数据
    for (const row of selectedRows.value) {
      const dataId = row[primaryKeyField.value]
      await OnlineTableApi.deleteTableData({
        tableId: tableConfig.value!.tableId,
        dataId: dataId
      })
    }

    ElMessage.success('批量删除成功')

    // 清空选择
    tableRef.value?.clearSelection()
    selectedRows.value = []

    await getTableData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

/** 导出数据 */
const handleExport = async () => {
  try {
    // 构建查询条件
    const queryConditions: Record<string, any> = {}

    // 添加查询条件
    queryFields.value.forEach(field => {
      const value = queryParams[field.columnName]
      if (value !== undefined && value !== null && value !== '') {
        if (field.queryType === 2) {
          queryConditions[field.columnName + '_like'] = value
        } else if (field.queryType === 3) {
          if (Array.isArray(value) && value.length === 2) {
            queryConditions[field.columnName + '_start'] = value[0]
            queryConditions[field.columnName + '_end'] = value[1]
          }
        } else {
          queryConditions[field.columnName] = value
        }
      }
    })

    // 添加排序参数
    if (sortParams.value.sortField) {
      queryConditions['sortField'] = sortParams.value.sortField
      queryConditions['sortOrder'] = sortParams.value.sortOrder
    }

    const params: OnlineTableApi.OnlineTableDataQueryDTO = {
      pageNo: 1,
      pageSize: 10000, // 导出所有数据
      tableId: tableConfig.value!.tableId,
      queryParams: queryConditions
    }

    // 调用后端导出接口
    await OnlineTableApi.exportTableData(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

/** 导入数据 */
const handleImport = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = async (e: any) => {
    const file = e.target.files[0]
    if (!file) return

    try {
      ElMessage.info('正在导入数据，请稍候...')
      const result = await OnlineTableApi.importTableData(tableConfig.value!.tableId, file)

      if (result.successCount > 0) {
        ElMessage.success(`导入成功 ${result.successCount} 条数据`)
        await getTableData() // 刷新列表
      }

      if (result.failureCount > 0) {
        ElMessage.warning(`导入失败 ${result.failureCount} 条数据`)
        console.warn('导入失败的数据:', result.failureList)
        console.warn('错误信息:', result.errorMessages)
      }

      if (result.successCount === 0 && result.failureCount === 0) {
        ElMessage.warning('没有导入任何数据')
      }
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败')
    }
  }
  input.click()
}

/** 下载导入模板 */
const handleDownloadTemplate = async () => {
  try {
    await OnlineTableApi.downloadImportTemplate(tableConfig.value!.tableId)
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}

/** 打开表单 */
const openForm = (type: 'create' | 'update', row?: any) => {
  dynamicFormRef.value?.open(type, row)
}

/** 查看详情 */
const handleView = (row: any) => {
  detailViewRef.value?.open(row)
}

/** 从详情页编辑 */
const handleEditFromDetail = (data: any) => {
  openForm('update', data)
}

/** 删除 */
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('是否确认删除该数据项?', '警告', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    const dataId = row[primaryKeyField.value]
    await OnlineTableApi.deleteTableData({
      tableId: tableConfig.value!.tableId,
      dataId: dataId
    })
    ElMessage.success('删除成功')
    await getTableData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

/** 返回 */
const goBack = () => {
  router.back()
}

// ========== 工具方法 ==========

/** 获取列对齐方式 */
const getColumnAlign = (field: any) => {
  if (isNumberField(field)) return 'right'
  if (field.componentType === 'switch') return 'center'
  return 'left'
}

/** 判断是否为日期时间字段 */
const isDateTimeField = (field: any) => {
  return ['date-picker', 'datetime-picker', 'time-picker'].includes(field.componentType)
}

/** 判断是否为数字字段 */
const isNumberField = (field: any) => {
  return field.componentType === 'input-number' ||
         ['int', 'bigint', 'decimal', 'float', 'double'].includes(field.columnType?.toLowerCase())
}

/** 判断是否为布尔字段 */
const isBooleanField = (field: any) => {
  return field.componentType === 'switch' || field.columnType?.toLowerCase() === 'boolean'
}

/** 判断是否为长文本字段 */
const isLongTextField = (field: any) => {
  return field.componentType === 'textarea' || field.columnType?.toLowerCase() === 'text'
}

/** 格式化日期时间 */
const formatDateTime = (value: any, componentType: string) => {
  if (!value) return ''

  try {
    const date = new Date(value)
    if (componentType === 'date-picker') {
      return date.toLocaleDateString()
    } else if (componentType === 'time-picker') {
      return date.toLocaleTimeString()
    } else {
      return date.toLocaleString()
    }
  } catch {
    return value
  }
}

/** 格式化数字 */
const formatNumber = (value: any) => {
  if (value === null || value === undefined || value === '') return ''

  const num = Number(value)
  if (isNaN(num)) return value

  return num.toLocaleString()
}

/** 截断文本 */
const truncateText = (text: string, maxLength: number) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

onMounted(() => {
  getTableConfig()
})
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.search-wrapper {
  margin-bottom: 10px;

  :deep(.el-card__body) {
    padding: 15px;
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__body-wrapper {
    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    background-color: #f5f7fa;
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #fafafa;
  }
}

:deep(.el-form) {
  .el-form-item {
    margin-bottom: 18px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }

    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-picker,
      .el-time-picker {
        width: 100%;
      }
    }
  }
}

:deep(.el-descriptions) {
  .el-descriptions__header {
    margin-bottom: 20px;
  }

  .el-descriptions__body {
    .el-descriptions__table {
      .el-descriptions__cell {
        padding: 12px 15px;

        &.is-bordered-label {
          background-color: #fafafa;
          font-weight: 500;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;

    .header-actions {
      justify-content: center;
    }
  }

  :deep(.el-form--inline) {
    .el-form-item {
      display: block;
      margin-right: 0;
      margin-bottom: 15px;

      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }

  :deep(.el-table) {
    font-size: 12px;

    .el-table__header-wrapper,
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}

// 打印样式
@media print {
  .card-header,
  .search-wrapper,
  .el-pagination,
  .el-table-column--selection,
  .el-table__fixed-right {
    display: none !important;
  }

  :deep(.el-table) {
    border: 1px solid #ddd;

    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
    }
  }
}
</style>
