<script lang="ts" setup>
import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteTableData,
  exportTableData,
  getOnlineTable,
  queryTableData,
} from '#/api/infra/onlineTable';
import { getDictOptions } from '#/utils/dict';

import DetailView from './components/DetailView.vue';
import DynamicForm from './components/DynamicForm.vue';

defineOptions({ name: 'OnlineTablePreview' });

const route = useRoute();
const router = useRouter();

// 表单配置
const tableConfig = ref<null | OnlineTableVO>(null);
const loading = ref(false);

// 计算属性
const isReadOnlyTable = computed(() => {
  return tableConfig.value?.tableType === 3; // 假设类型3为只读表
});

// 动态生成表格列配置
const useGridColumns = (): VxeTableGridOptions['columns'] => {
  if (!tableConfig.value?.columns) return [];

  return [
    ...tableConfig.value.columns
      .filter((col) => col.listFlag === 1)
      .sort((a, b) => a.sort - b.sort)
      .map((col) => ({
        field: col.columnName,
        title: col.columnComment,
        minWidth: 120,
        ...(col.dictType && {
          cellRender: {
            name: 'CellDict',
            props: { type: col.dictType },
          },
        }),
        ...(col.columnType.includes('TIME') && {
          formatter: 'formatDateTime',
        }),
      })),
    {
      title: '操作',
      width: 180,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
};

// 动态生成搜索表单配置
const useSearchFormSchema = (): VbenFormSchema[] => {
  if (!tableConfig.value?.columns) return [];

  return tableConfig.value.columns
    .filter((col) => col.queryFlag === 1)
    .sort((a, b) => a.sort - b.sort)
    .map((col) => ({
      fieldName: col.columnName,
      label: col.columnComment,
      component: getSearchComponent(col.componentType),
      componentProps: {
        placeholder: `请输入${col.columnComment}`,
        allowClear: true,
        ...(col.dictType && {
          options: getDictOptions(col.dictType),
        }),
      },
    }));
};

// 根据组件类型获取搜索组件
const getSearchComponent = (componentType: string) => {
  switch (componentType) {
    case 'date': {
      return 'DatePicker';
    }
    case 'datetime': {
      return 'DatePicker';
    }
    case 'number': {
      return 'InputNumber';
    }
    case 'radio':
    case 'select': {
      return 'Select';
    }
    default: {
      return 'Input';
    }
  }
};

// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useSearchFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    rowConfig: {
      keyField: 'id',
    },
    pagerConfig: {
      enabled: true,
      pageSize: 20,
      pageSizes: [10, 20, 50, 100],
    },
    proxyConfig: {
      ajax: {
        query: async ({ page, sorts, filters, form }: any) => {
          if (!tableConfig.value) return { list: [], total: 0 };

          const params = {
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            tableId: tableConfig.value.tableId,
            queryParams: form,
          };

          try {
            const res = await queryTableData(params);
            return {
              list: res?.list || [],
              total: res?.total || 0,
            };
          } catch (error) {
            console.error('获取表格数据失败', error);
            message.error('获取表格数据失败');
            return { list: [], total: 0 };
          }
        },
      },
    },
  },
});

// 动态表单弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: DynamicForm,
});

// 详情查看弹窗
const [DetailModal, detailModalApi] = useVbenModal({
  connectedComponent: DetailView,
});

/** 获取表单配置 */
const getTableConfig = async () => {
  const tableId = route.params.id as string;
  if (!tableId) {
    message.error('表单ID不能为空');
    return;
  }

  loading.value = true;
  try {
    const res = await getOnlineTable(tableId);
    tableConfig.value = res;

    // 重新初始化表格
    gridApi.reload();
  } catch (error) {
    console.error('获取表单配置失败', error);
    message.error('获取表单配置失败');
  } finally {
    loading.value = false;
  }
};

/** 新增数据 */
const handleCreate = () => {
  formModalApi.open({
    title: '新增数据',
    data: { type: 'create', tableConfig: tableConfig.value },
  });
};

/** 编辑数据 */
const handleEdit = (record: any) => {
  formModalApi.open({
    title: '编辑数据',
    data: { type: 'update', record, tableConfig: tableConfig.value },
  });
};

/** 查看详情 */
const handleView = (record: any) => {
  detailModalApi.open({
    title: '查看详情',
    data: { record, tableConfig: tableConfig.value },
  });
};

/** 从详情页编辑 */
const handleEditFromDetail = (data: any) => {
  openForm('update', data);
};

/** 删除数据 */
const handleDelete = async (record: any) => {
  try {
    await deleteTableData(tableConfig.value!.tableId, record.id);
    message.success('删除成功');
    gridApi.reload();
  } catch (error) {
    console.error('删除失败', error);
    message.error('删除失败');
  }
};

/** 导出数据 */
const handleExport = async () => {
  try {
    await exportTableData(tableConfig.value!.tableId);
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败', error);
    message.error('导出失败');
  }
};

/** 返回 */
const goBack = () => {
  router.back();
};

// 注册组件
// Grid组件已通过useVbenVxeGrid自动注册

/** 初始化 */
onMounted(() => {
  getTableConfig();
});
</script>

<template>
  <Page auto-content-height>
    <Grid :table-title="tableConfig?.tableComment || '在线表单预览'">
      <!-- 工具栏按钮 -->
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: '新增',
              type: 'primary',
              icon: ACTION_ICON.ADD,
              onClick: handleCreate,
              ifShow: !isReadOnlyTable,
            },
            {
              label: '导出',
              icon: ACTION_ICON.DOWNLOAD,
              onClick: handleExport,
              ifShow: tableConfig?.page === 1,
            },
            {
              label: '返回',
              icon: 'lucide:arrow-left',
              onClick: goBack,
            },
          ]"
        />
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: '查看',
              type: 'link',
              onClick: () => handleView(row),
            },
            {
              label: '编辑',
              type: 'link',
              icon: ACTION_ICON.EDIT,
              onClick: () => handleEdit(row),
              ifShow: !isReadOnlyTable,
            },
            {
              label: '删除',
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              ifShow: !isReadOnlyTable,
              popConfirm: {
                title: '是否确认删除该条数据?',
                confirm: () => handleDelete(row),
              },
            },
          ]"
        />
      </template>
    </Grid>

    <!-- 动态表单弹窗 -->
    <FormModal />

    <!-- 详情查看弹窗 -->
    <DetailModal />
  </Page>
</template>
