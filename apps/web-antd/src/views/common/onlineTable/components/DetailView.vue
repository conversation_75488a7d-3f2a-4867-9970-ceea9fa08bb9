<template>
  <VbenModal
    v-bind="$attrs"
    title="查看详情"
    :width="800"
    :show-ok="false"
    cancel-text="关闭"
  >
    <a-descriptions :column="2" bordered size="small">
      <a-descriptions-item
        v-for="field in formFields"
        :key="field.columnName"
        :label="field.columnComment"
      >
        <!-- 字典类型显示 -->
        <a-tag
          v-if="field.dictType"
          color="blue"
        >
          {{ getDictLabel(field.dictType, viewData[field.columnName]) }}
        </a-tag>
        <!-- 日期时间格式化显示 -->
        <span v-else-if="isDateTimeField(field)">
          {{ formatDateTime(viewData[field.columnName]) }}
        </span>
        <!-- 数字格式化显示 -->
        <span v-else-if="isNumberField(field)">
          {{ formatNumber(viewData[field.columnName]) }}
        </span>
        <!-- 布尔值显示 -->
        <a-tag v-else-if="isBooleanField(field)" :color="viewData[field.columnName] ? 'green' : 'default'">
          {{ viewData[field.columnName] ? '是' : '否' }}
        </a-tag>
        <!-- 长文本显示 -->
        <div v-else-if="isLongTextField(field)" class="max-w-md">
          <a-typography-paragraph :ellipsis="{ rows: 3, expandable: true }">
            {{ viewData[field.columnName] }}
          </a-typography-paragraph>
        </div>
        <!-- 颜色显示 -->
        <div v-else-if="field.componentType === 'color-picker'" class="flex items-center gap-2">
          <div
            class="w-6 h-6 rounded border"
            :style="{ backgroundColor: viewData[field.columnName] }"
          ></div>
          <span>{{ viewData[field.columnName] }}</span>
        </div>
        <!-- 评分显示 -->
        <a-rate
          v-else-if="field.componentType === 'rate'"
          :value="viewData[field.columnName]"
          disabled
          :count="5"
        />
        <!-- 滑块显示 -->
        <div v-else-if="field.componentType === 'slider'" class="flex items-center gap-2">
          <a-progress
            :percent="(viewData[field.columnName] / (field.componentLength || 100)) * 100"
            :show-info="false"
            class="flex-1"
          />
          <span>{{ viewData[field.columnName] }}</span>
        </div>
        <!-- 多选值显示 -->
        <div v-else-if="Array.isArray(viewData[field.columnName])" class="flex flex-wrap gap-1">
          <a-tag
            v-for="item in viewData[field.columnName]"
            :key="item"
            color="blue"
          >
            {{ getDictLabel(field.dictType, item) || item }}
          </a-tag>
        </div>
        <!-- 普通文本显示 -->
        <span v-else>{{ viewData[field.columnName] || '-' }}</span>
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <div class="flex justify-end gap-2">
        <a-button @click="handleClose">关闭</a-button>
        <a-button v-if="!isReadOnly" type="primary" @click="handleEdit">编辑</a-button>
      </div>
    </template>
  </VbenModal>
</template>

<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { computed, ref } from 'vue';

import { VbenModal, useVbenModal } from '@vben/common-ui';
import { formatDateTime } from '@vben/utils';

import { Icon } from '@iconify/vue';

import { getDictOptions } from '#/utils/dict';

interface Props {
  tableConfig: OnlineTableVO | null;
  isReadOnly?: boolean;
}

interface Emits {
  (e: 'edit', data: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  isReadOnly: false,
});

const emit = defineEmits<Emits>();

const viewData = ref<any>({});

// 计算属性
const formFields = computed(() => {
  if (!props.tableConfig?.columns) return [];

  return props.tableConfig.columns
    .filter(col => col.showFlag === 1)
    .sort((a, b) => a.sort - b.sort);
});

/** 关闭详情 */
const handleClose = () => {
  modalApi.close();
};

/** 编辑 */
const handleEdit = () => {
  emit('edit', { ...viewData.value });
  handleClose();
};

// ========== 工具方法 ==========

/** 判断是否为日期时间字段 */
const isDateTimeField = (field: any) => {
  return ['date', 'datetime', 'time'].includes(field.componentType);
};

/** 判断是否为数字字段 */
const isNumberField = (field: any) => {
  return field.componentType === 'number' ||
         ['INT', 'BIGINT', 'DECIMAL', 'FLOAT', 'DOUBLE'].includes(field.columnType?.toUpperCase());
};

/** 判断是否为布尔字段 */
const isBooleanField = (field: any) => {
  return field.componentType === 'switch' || field.columnType?.toUpperCase() === 'TINYINT';
};

/** 判断是否为长文本字段 */
const isLongTextField = (field: any) => {
  return field.componentType === 'textarea' || field.columnType?.toUpperCase() === 'TEXT';
};

/** 格式化日期时间 */
const formatDateTime = (value: any, componentType: string) => {
  if (!value) return '-'
  
  try {
    const date = new Date(value)
    if (componentType === 'date-picker') {
      return date.toLocaleDateString()
    } else if (componentType === 'time-picker') {
      return date.toLocaleTimeString()
    } else {
      return date.toLocaleString()
    }
  } catch {
    return value
  }
}

/** 格式化数字 */
const formatNumber = (value: any) => {
  if (value === null || value === undefined || value === '') return '-'
  
  const num = Number(value)
  if (isNaN(num)) return value
  
  return num.toLocaleString()
}

/** 获取字典标签 */
const getDictLabel = (dictType: string, value: any) => {
  if (!dictType) return value;

  const options = getDictOptions(dictType);
  const option = options.find(opt => opt.value === value);
  return option?.label || value;
};

/** 获取文件列表 */
const getFileList = (field: any) => {
  const value = viewData.value[field.columnName];
  if (!value) return [];

  try {
    if (typeof value === 'string') {
      return JSON.parse(value);
    }
    if (Array.isArray(value)) {
      return value;
    }
  } catch {
    // 解析失败，返回空数组
  }

  return [];
};

// 弹窗方法
const modalApi = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      viewData.value = {};
    }
  },
});

// 暴露方法给父组件
defineExpose({
  open: (data: { record: any; tableConfig: OnlineTableVO }) => {
    viewData.value = { ...data.record };
    modalApi.open();
  },
});
</script>


