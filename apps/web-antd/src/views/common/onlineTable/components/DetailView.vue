<template>
  <el-dialog
    title="查看详情"
    v-model="dialogVisible"
    :width="getDialogWidth()"
    append-to-body
    destroy-on-close
    @close="handleClose"
  >
    <el-descriptions :column="getColumnCount()" border>
      <el-descriptions-item
        v-for="field in formFields"
        :key="field.columnName"
        :label="field.columnText"
        :span="getFieldSpan(field)"
      >
        <!-- 字典类型显示 -->
        <dict-tag 
          v-if="field.dictType" 
          :type="field.dictType" 
          :value="viewData[field.columnName]" 
        />
        <!-- 日期时间格式化显示 -->
        <span v-else-if="isDateTimeField(field)">
          {{ formatDateTime(viewData[field.columnName], field.componentType) }}
        </span>
        <!-- 数字格式化显示 -->
        <span v-else-if="isNumberField(field)">
          {{ formatNumber(viewData[field.columnName]) }}
        </span>
        <!-- 布尔值显示 -->
        <el-tag v-else-if="isBooleanField(field)" :type="viewData[field.columnName] ? 'success' : 'info'">
          {{ viewData[field.columnName] ? '是' : '否' }}
        </el-tag>
        <!-- 长文本显示 -->
        <div v-else-if="isLongTextField(field)" class="long-text">
          {{ viewData[field.columnName] }}
        </div>
        <!-- 颜色显示 -->
        <div v-else-if="field.componentType === 'color-picker'" class="color-display">
          <div 
            class="color-block" 
            :style="{ backgroundColor: viewData[field.columnName] }"
          ></div>
          <span>{{ viewData[field.columnName] }}</span>
        </div>
        <!-- 评分显示 -->
        <el-rate 
          v-else-if="field.componentType === 'rate'"
          :model-value="viewData[field.columnName]"
          disabled
          show-score
        />
        <!-- 滑块显示 -->
        <div v-else-if="field.componentType === 'slider'" class="slider-display">
          <el-progress 
            :percentage="(viewData[field.columnName] / (field.componentLength || 100)) * 100"
            :show-text="false"
          />
          <span class="slider-value">{{ viewData[field.columnName] }}</span>
        </div>
        <!-- 多选值显示 -->
        <div v-else-if="Array.isArray(viewData[field.columnName])" class="multi-value">
          <el-tag 
            v-for="item in viewData[field.columnName]" 
            :key="item"
            size="small"
            class="mr-5px"
          >
            {{ getDictLabel(field.dictType, item) || item }}
          </el-tag>
        </div>
        <!-- 普通文本显示 -->
        <span v-else>{{ viewData[field.columnName] || '-' }}</span>
      </el-descriptions-item>
    </el-descriptions>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
        <el-button type="primary" @click="handleEdit" v-if="!isReadOnly">编 辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getDictOptions } from '@/utils/dict'
import * as OnlineTableApi from '@/api/infra/onlineTable'
import { reactive, ref, computed } from 'vue'

interface Props {
  tableConfig?: OnlineTableApi.OnlineTableVO
  isReadOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isReadOnly: false
})

const emit = defineEmits<{
  edit: [data: any]
}>()

const dialogVisible = ref(false)
const viewData = reactive<any>({})

// 计算属性
const formFields = computed(() => {
  return props.tableConfig?.pageConfigs?.filter(field => field.formShowFlag)?.sort((a, b) => (a.sort || 0) - (b.sort || 0)) || []
})

/** 打开详情 */
const open = (data: any) => {
  dialogVisible.value = true
  
  // 清空并设置数据
  Object.keys(viewData).forEach(key => {
    delete viewData[key]
  })
  Object.assign(viewData, data)
}

/** 关闭详情 */
const handleClose = () => {
  dialogVisible.value = false
}

/** 编辑 */
const handleEdit = () => {
  emit('edit', { ...viewData })
  handleClose()
}

// ========== 工具方法 ==========

/** 获取对话框宽度 */
const getDialogWidth = () => {
  const fieldCount = formFields.value.length
  if (fieldCount <= 6) return '800px'
  if (fieldCount <= 12) return '1000px'
  return '1200px'
}

/** 获取列数 */
const getColumnCount = () => {
  const fieldCount = formFields.value.length
  if (fieldCount <= 6) return 1
  if (fieldCount <= 12) return 2
  return 3
}

/** 获取字段跨度 */
const getFieldSpan = (field: any) => {
  if (field.componentType === 'textarea') return getColumnCount()
  if (isLongTextField(field)) return getColumnCount()
  return 1
}

/** 判断是否为日期时间字段 */
const isDateTimeField = (field: any) => {
  return ['date-picker', 'datetime-picker', 'time-picker'].includes(field.componentType)
}

/** 判断是否为数字字段 */
const isNumberField = (field: any) => {
  return field.componentType === 'input-number' || 
         ['int', 'bigint', 'decimal', 'float', 'double'].includes(field.columnType?.toLowerCase())
}

/** 判断是否为布尔字段 */
const isBooleanField = (field: any) => {
  return field.componentType === 'switch' || field.columnType?.toLowerCase() === 'boolean'
}

/** 判断是否为长文本字段 */
const isLongTextField = (field: any) => {
  return field.componentType === 'textarea' || field.columnType?.toLowerCase() === 'text'
}

/** 格式化日期时间 */
const formatDateTime = (value: any, componentType: string) => {
  if (!value) return '-'
  
  try {
    const date = new Date(value)
    if (componentType === 'date-picker') {
      return date.toLocaleDateString()
    } else if (componentType === 'time-picker') {
      return date.toLocaleTimeString()
    } else {
      return date.toLocaleString()
    }
  } catch {
    return value
  }
}

/** 格式化数字 */
const formatNumber = (value: any) => {
  if (value === null || value === undefined || value === '') return '-'
  
  const num = Number(value)
  if (isNaN(num)) return value
  
  return num.toLocaleString()
}

/** 获取字典标签 */
const getDictLabel = (dictType: string, value: any) => {
  if (!dictType) return value
  
  const options = getDictOptions(dictType)
  const option = options.find(opt => opt.value === value)
  return option?.label || value
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  .el-dialog__header {
    background-color: #f5f7fa;
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    
    .el-dialog__title {
      font-weight: 600;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #fafafa;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__header {
    margin-bottom: 20px;
  }
  
  .el-descriptions__body {
    .el-descriptions__table {
      .el-descriptions__cell {
        padding: 12px 15px;
        
        &.is-bordered-label {
          background-color: #fafafa;
          font-weight: 500;
          width: 120px;
        }
      }
    }
  }
}

.long-text {
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.5;
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .color-block {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
  }
}

.slider-display {
  display: flex;
  align-items: center;
  gap: 10px;
  
  .el-progress {
    flex: 1;
  }
  
  .slider-value {
    font-weight: 500;
    color: #409eff;
  }
}

.multi-value {
  .el-tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-descriptions) {
    .el-descriptions__body {
      .el-descriptions__table {
        .el-descriptions__cell {
          &.is-bordered-label {
            width: 80px;
          }
        }
      }
    }
  }
  
  .long-text {
    max-height: 150px;
  }
}
</style>
