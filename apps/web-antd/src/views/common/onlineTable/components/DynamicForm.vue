<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    :width="getDialogWidth()"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="getLabelWidth()"
      v-loading="formLoading"
    >
      <!-- 没有表单字段时的提示 -->
      <div v-if="formFields.length === 0" class="no-fields-tip">
        <el-empty description="暂无可编辑的表单字段">
          <template #image>
            <Icon icon="ep:document" size="60" />
          </template>
          <p>请在表单配置中设置字段的"表单显示"属性为"是"</p>
        </el-empty>
      </div>

      <el-row :gutter="20" v-else>
        <el-col
          v-for="field in formFields"
          :key="field.columnName"
          :span="getFieldSpan(field)"
        >
          <el-form-item
            :label="field.columnText || field.columnName"
            :prop="field.columnName"
            :required="field.requiredFlag"
          >
            <!-- 输入框 -->
            <el-input
              v-if="field.componentType === 'input'"
              v-model="formData[field.columnName]"
              :placeholder="`请输入${field.columnText}`"
              :readonly="field.readonlyFlag"
              :maxlength="field.componentLength"
              clearable
            />
            <!-- 密码输入框 -->
            <el-input
              v-else-if="field.componentType === 'password'"
              v-model="formData[field.columnName]"
              type="password"
              :placeholder="`请输入${field.columnText}`"
              :readonly="field.readonlyFlag"
              show-password
              clearable
            />
            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.componentType === 'input-number'"
              v-model="formData[field.columnName]"
              :placeholder="`请输入${field.columnText}`"
              :readonly="field.readonlyFlag"
              :precision="getNumberPrecision(field)"
              style="width: 100%"
            />
            <!-- 文本域 -->
            <el-input
              v-else-if="field.componentType === 'textarea'"
              v-model="formData[field.columnName]"
              type="textarea"
              :rows="getTextareaRows(field)"
              :placeholder="`请输入${field.columnText}`"
              :readonly="field.readonlyFlag"
              :maxlength="field.componentLength"
              show-word-limit
            />
            <!-- 选择器 -->
            <el-select
              v-else-if="field.componentType === 'select'"
              v-model="formData[field.columnName]"
              :placeholder="`请选择${field.columnText}`"
              :disabled="field.readonlyFlag"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="dict in getDictOptions(field.dictType)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <!-- 多选选择器 -->
            <el-select
              v-else-if="field.componentType === 'multi-select'"
              v-model="formData[field.columnName]"
              :placeholder="`请选择${field.columnText}`"
              :disabled="field.readonlyFlag"
              multiple
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="dict in getDictOptions(field.dictType)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <!-- 单选框组 -->
            <el-radio-group
              v-else-if="field.componentType === 'radio'"
              v-model="formData[field.columnName]"
              :disabled="field.readonlyFlag"
            >
              <el-radio
                v-for="dict in getDictOptions(field.dictType)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
            <!-- 复选框组 -->
            <el-checkbox-group
              v-else-if="field.componentType === 'checkbox'"
              v-model="formData[field.columnName]"
              :disabled="field.readonlyFlag"
            >
              <el-checkbox
                v-for="dict in getDictOptions(field.dictType)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-checkbox>
            </el-checkbox-group>
            <!-- 开关 -->
            <el-switch
              v-else-if="field.componentType === 'switch'"
              v-model="formData[field.columnName]"
              :disabled="field.readonlyFlag"
              active-text="是"
              inactive-text="否"
            />
            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="field.componentType === 'date-picker'"
              v-model="formData[field.columnName]"
              type="date"
              :placeholder="`请选择${field.columnText}`"
              :disabled="field.readonlyFlag"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
            <!-- 日期时间选择器 -->
            <el-date-picker
              v-else-if="field.componentType === 'datetime-picker'"
              v-model="formData[field.columnName]"
              type="datetime"
              :placeholder="`请选择${field.columnText}`"
              :disabled="field.readonlyFlag"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
            <!-- 时间选择器 -->
            <el-time-picker
              v-else-if="field.componentType === 'time-picker'"
              v-model="formData[field.columnName]"
              :placeholder="`请选择${field.columnText}`"
              :disabled="field.readonlyFlag"
              value-format="HH:mm:ss"
              style="width: 100%"
            />
            <!-- 日期范围选择器 -->
            <el-date-picker
              v-else-if="field.componentType === 'daterange-picker'"
              v-model="formData[field.columnName]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled="field.readonlyFlag"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
            <!-- 滑块 -->
            <el-slider
              v-else-if="field.componentType === 'slider'"
              v-model="formData[field.columnName]"
              :disabled="field.readonlyFlag"
              :max="field.componentLength || 100"
              show-input
            />
            <!-- 评分 -->
            <el-rate
              v-else-if="field.componentType === 'rate'"
              v-model="formData[field.columnName]"
              :disabled="field.readonlyFlag"
              :max="field.componentLength || 5"
              show-score
            />
            <!-- 颜色选择器 -->
            <el-color-picker
              v-else-if="field.componentType === 'color-picker'"
              v-model="formData[field.columnName]"
              :disabled="field.readonlyFlag"
              show-alpha
            />
            <!-- 默认输入框 -->
            <el-input
              v-else
              v-model="formData[field.columnName]"
              :placeholder="`请输入${field.columnText}`"
              :readonly="field.readonlyFlag"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="formLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getDictOptions } from '@/utils/dict'
import * as OnlineTableApi from '@/api/infra/onlineTable'
import { reactive, ref, computed, watch, nextTick } from 'vue'

interface Props {
  tableConfig?: OnlineTableApi.OnlineTableVO
}

const props = defineProps<Props>()

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const formLoading = ref(false)
const formType = ref<'create' | 'update'>('create')
const formRef = ref()

const formData = reactive<any>({})
const formRules = reactive<any>({})

// 计算属性
const formFields = computed(() => {
  return props.tableConfig?.pageConfigs?.filter(field => field.formShowFlag)?.sort((a, b) => (a.sort || 0) - (b.sort || 0)) || []
})

const dialogTitle = computed(() => {
  return formType.value === 'create' ? '新增' : '编辑'
})

/** 初始化表单验证规则 */
const initFormRules = () => {
  // 清空现有规则
  Object.keys(formRules).forEach(key => {
    delete formRules[key]
  })
  
  formFields.value.forEach(field => {
    const rules: any[] = []
    
    // 必填验证
    if (field.requiredFlag) {
      rules.push({ 
        required: true, 
        message: `${field.columnText}不能为空`, 
        trigger: ['blur', 'change'] 
      })
    }
    
    // 自定义验证规则
    if (field.validateRule) {
      try {
        const regex = new RegExp(field.validateRule)
        rules.push({
          pattern: regex,
          message: `${field.columnText}格式不正确`,
          trigger: 'blur'
        })
      } catch (e) {
        console.warn(`字段 ${field.columnName} 的验证规则格式错误:`, field.validateRule)
      }
    }
    
    // 长度验证
    if (field.componentLength && field.componentType === 'input') {
      rules.push({
        max: field.componentLength,
        message: `${field.columnText}长度不能超过${field.componentLength}个字符`,
        trigger: 'blur'
      })
    }
    
    if (rules.length > 0) {
      formRules[field.columnName] = rules
    }
  })
}

// 监听表单字段变化，初始化验证规则
watch(() => formFields.value, () => {
  initFormRules()
}, { immediate: true })

/** 打开表单 */
const open = (type: 'create' | 'update', data?: any) => {
  formType.value = type
  dialogVisible.value = true
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  
  if (type === 'update' && data) {
    Object.assign(formData, data)
  } else {
    // 设置默认值
    formFields.value.forEach(field => {
      if (field.componentDefaultValue) {
        formData[field.columnName] = field.componentDefaultValue
      }
    })
  }
  
  // 清除验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!props.tableConfig) return

  try {
    await formRef.value?.validate()

    formLoading.value = true

    if (formType.value === 'create') {
      await OnlineTableApi.createTableData({
        tableId: props.tableConfig.tableId,
        formData: { ...formData }
      })
      ElMessage.success('新增成功')
    } else {
      // 获取主键字段
      const primaryKeyField = props.tableConfig.columns?.find(col => col.privateKey)?.columnName || 'id'
      const dataId = formData[primaryKeyField]

      await OnlineTableApi.updateTableData({
        tableId: props.tableConfig.tableId,
        dataId: dataId,
        formData: { ...formData }
      })
      ElMessage.success('修改成功')
    }

    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

/** 关闭表单 */
const handleClose = () => {
  dialogVisible.value = false
}

// ========== 工具方法 ==========

/** 获取对话框宽度 */
const getDialogWidth = () => {
  const fieldCount = formFields.value.length
  if (fieldCount === 0) return '500px'
  if (fieldCount <= 2) return '500px'
  if (fieldCount <= 4) return '600px'
  if (fieldCount <= 6) return '700px'
  return '800px'
}

/** 获取标签宽度 */
const getLabelWidth = () => {
  if (formFields.value.length === 0) return '100px'
  const maxLabelLength = Math.max(...formFields.value.map(f => {
    const textLength = (f.columnText || '').length
    return Math.max(textLength * 14 + 20, 80)
  }))
  return `${Math.min(maxLabelLength, 150)}px`
}

/** 获取字段跨度 */
const getFieldSpan = (field: any) => {
  if (field.componentType === 'textarea') return 24
  if (field.componentType === 'daterange-picker') return 24

  // 根据字段数量自动调整跨度
  const fieldCount = formFields.value.length
  if (fieldCount === 1) return 24
  if (fieldCount === 2) return 24
  if (fieldCount <= 4) return 12
  return 8
}

/** 获取数字精度 */
const getNumberPrecision = (field: any) => {
  if (field.columnType?.includes('decimal') || field.columnType?.includes('float')) {
    return 2
  }
  return 0
}

/** 获取文本域行数 */
const getTextareaRows = (field: any) => {
  return field.componentLength ? Math.min(Math.max(field.componentLength / 50, 3), 10) : 3
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  .el-dialog__header {
    background-color: #f5f7fa;
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    
    .el-dialog__title {
      font-weight: 600;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #fafafa;
  }
}

:deep(.el-form) {
  .el-form-item {
    margin-bottom: 18px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
    
    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-picker,
      .el-time-picker {
        width: 100%;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.no-fields-tip {
  padding: 40px 20px;
  text-align: center;

  p {
    margin-top: 10px;
    color: #909399;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-form) {
    .el-form-item {
      .el-form-item__content {
        .el-input,
        .el-select,
        .el-date-picker,
        .el-time-picker {
          width: 100%;
        }
      }
    }
  }
}
</style>
