<template>
  <VbenModal
    v-bind="$attrs"
    :title="getTitle"
    :loading="loading"
    :width="800"
    @ok="handleSubmit"
  >
    <div v-if="formFields.length === 0" class="flex items-center justify-center py-8">
      <a-empty description="暂无可编辑的表单字段">
        <template #image>
          <Icon icon="ant-design:file-text-outlined" class="text-4xl" />
        </template>
        <p class="text-gray-500">请在表单配置中设置字段的"表单显示"属性为"是"</p>
      </a-empty>
    </div>

    <VbenForm
      v-else
      @register="registerForm"
    />
  </VbenModal>
</template>

<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { computed, ref, watch } from 'vue';

import { VbenModal, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';

import { useVbenForm } from '#/adapter/form';
import {
  createTableData,
  updateTableData,
} from '#/api/infra/onlineTable';
import { getDictOptions } from '#/utils/dict';

interface Props {
  tableConfig: OnlineTableVO | null;
}

interface Emits {
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表单数据
const formData = ref<Record<string, any>>({});
const formType = ref<'create' | 'update'>('create');
const loading = ref(false);

// 计算属性
const getTitle = computed(() => {
  return formType.value === 'create' ? '新增数据' : '编辑数据';
});

// 获取表单字段
const formFields = computed(() => {
  if (!props.tableConfig?.columns) return [];

  return props.tableConfig.columns
    .filter(col => col.formFlag === 1)
    .sort((a, b) => a.sort - b.sort);
});

// 动态表单
const [VbenForm, formApi] = useVbenForm({
  schema: [],
  showDefaultActions: false,
  wrapperClass: 'grid grid-cols-1 md:grid-cols-2 gap-4',
});

// 监听表单字段变化，动态生成schema
watch(
  () => formFields.value,
  (fields) => {
    if (fields.length === 0) return;

    const schema = fields.map(field => ({
      fieldName: field.columnName,
      label: field.columnComment,
      component: getFormComponent(field.componentType),
      componentProps: getComponentProps(field),
      rules: getFieldRules(field),
    }));

    formApi.updateSchema(schema);
  },
  { immediate: true },
);

// 根据组件类型获取表单组件
const getFormComponent = (componentType: string) => {
  switch (componentType) {
    case 'textarea':
      return 'Textarea';
    case 'number':
      return 'InputNumber';
    case 'select':
      return 'Select';
    case 'radio':
      return 'RadioGroup';
    case 'checkbox':
      return 'CheckboxGroup';
    case 'switch':
      return 'Switch';
    case 'date':
      return 'DatePicker';
    case 'datetime':
      return 'DatePicker';
    case 'upload':
      return 'Upload';
    default:
      return 'Input';
  }
};

// 获取组件属性
const getComponentProps = (field: any) => {
  const baseProps = {
    placeholder: `请输入${field.columnComment}`,
    allowClear: true,
  };

  switch (field.componentType) {
    case 'textarea':
      return {
        ...baseProps,
        rows: 4,
      };
    case 'number':
      return {
        ...baseProps,
        min: 0,
        style: { width: '100%' },
      };
    case 'select':
      return {
        ...baseProps,
        placeholder: `请选择${field.columnComment}`,
        options: getFieldOptions(field),
      };
    case 'datetime':
      return {
        ...baseProps,
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      };
    default:
      return baseProps;
  }
};

// 获取字段选项
const getFieldOptions = (field: any) => {
  // 这里可以根据字典类型或自定义选项返回选项列表
  if (field.dictType) {
    // 从字典获取选项
    return getDictOptions(field.dictType);
  }

  // 解析自定义选项
  if (field.componentProps) {
    try {
      const props = JSON.parse(field.componentProps);
      return props.options || [];
    } catch {
      return [];
    }
  }

  return [];
};

// 获取字段验证规则
const getFieldRules = (field: any) => {
  const rules = [];

  if (field.isNullable === 0) {
    rules.push({
      required: true,
      message: `请输入${field.columnComment}`,
    });
  }

  if (field.validateRules) {
    try {
      const customRules = JSON.parse(field.validateRules);
      rules.push(...customRules);
    } catch {
      // 忽略解析错误
    }
  }

  return rules;
};

/** 提交表单 */
const handleSubmit = async () => {
  try {
    // 验证表单
    await formApi.validate();

    const values = formApi.getValues();

    loading.value = true;

    if (formType.value === 'create') {
      await createTableData({
        tableId: props.tableConfig!.tableId,
        formData: values,
      });
      message.success('新增成功');
    } else {
      await updateTableData({
        tableId: props.tableConfig!.tableId,
        dataId: formData.value.id,
        formData: values,
      });
      message.success('修改成功');
    }

    emit('success');
    return true;
  } catch (error) {
    console.error('提交失败', error);
    return false;
  } finally {
    loading.value = false;
  }
};

// 弹窗方法
const modalApi = useVbenModal({
  onOpenChange: (isOpen: boolean) => {
    if (!isOpen) {
      formApi.resetForm();
      formData.value = {};
    }
  },
});

// 注册表单
const registerForm = formApi.register;

// 暴露方法给父组件
defineExpose({
  open: (data: { type: 'create' | 'update'; record?: any; tableConfig: OnlineTableVO }) => {
    formType.value = data.type;

    if (data.type === 'update' && data.record) {
      formData.value = { ...data.record };
      formApi.setValues(data.record);
    } else {
      formData.value = {};
      formApi.resetForm();
    }

    modalApi.open();
  },
});
</script>
