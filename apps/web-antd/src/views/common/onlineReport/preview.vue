<script lang="ts" setup>
import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { formatDate, formatDateTime } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import * as OnlineReportApi from '#/api/infra/online-report';
import { getDictOptions } from '#/utils/dict';

defineOptions({ name: 'OnlineReportPreview' });

// 定义props
const props = defineProps<{
  reportId?: number | string;
}>();

const route = useRoute();
const reportInfo = ref<OnlineReportApi.OnlineReportVO>();
const reportColumns = ref<OnlineReportApi.OnlineReportColumnVO[]>([]);
const reportParams = ref<OnlineReportApi.OnlineReportParamVO[]>([]);

/** 获取表格标题 */
const getTableTitle = () => {
  if (reportInfo.value?.name && reportInfo.value?.code) {
    return `${reportInfo.value.name} (${reportInfo.value.code})`;
  }
  return reportInfo.value?.name || reportInfo.value?.code || '在线报表';
};

/** 动态生成查询表单配置 */
const useGridFormSchema = (): VbenFormSchema[] => {
  return reportParams.value.map((param: any) => {
    const baseSchema: VbenFormSchema = {
      fieldName: param.paramName,
      label: param.paramText,
      component: 'Input',
      componentProps: {
        placeholder: `请输入${param.paramText}`,
        clearable: true,
      },
    };

    // 所有查询条件都是必填的
    baseSchema.rules = `required`;
    baseSchema.help = `${param.paramText}为必填项`;

    // 根据参数类型设置不同的组件
    switch (param.paramType) {
      case 'DATE': {
        baseSchema.component = 'DatePicker';
        baseSchema.componentProps = {
          placeholder: `请选择${param.paramText}`,
          allowClear: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        };

        break;
      }
      case 'DATETIME': {
        baseSchema.component = 'DatePicker';
        baseSchema.componentProps = {
          placeholder: `请选择${param.paramText}`,
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        };

        break;
      }
      case 'NUMBER': {
        baseSchema.component = 'InputNumber';
        baseSchema.componentProps = {
          placeholder: `请输入${param.paramText}`,
          style: { width: '100%' },
        };

        break;
      }
      default: {
        if (param.dictType) {
          // 字典类型
          baseSchema.component = 'Select';
          baseSchema.componentProps = {
            placeholder: `请选择${param.paramText}`,
            allowClear: true,
            options: getDictOptions(param.dictType),
          };
        }
      }
    }

    return baseSchema;
  });
};

/** 创建表格列配置 */
const getTableColumns = () => {
  return reportColumns.value.map((column: any) => {
    const baseColumn: any = {
      field: column.columnName,
      title: column.columnComment || column.columnName,
      width: column.width || 120,
      align: 'left',
      showOverflow: true,
    };

    // 根据字段类型设置格式化器
    if (column.javaType === 'Date' || column.javaType === 'LocalDateTime') {
      baseColumn.formatter = ({ cellValue }: any) => {
        if (!cellValue) return '';
        return column.javaType === 'Date'
          ? formatDate(cellValue)
          : formatDateTime(cellValue);
      };
    } else if (column.dictType) {
      // 字典类型字段
      baseColumn.formatter = ({ cellValue }: any) => {
        if (!cellValue) return '';
        const dictOptions = getDictOptions(column.dictType);
        const option = dictOptions.find((opt: any) => opt.value === cellValue);
        return option ? option.label : cellValue;
      };
    }

    return baseColumn;
  });
};

/** 获取报表信息 */
async function getReportInfo() {
  const reportId = props.reportId || route.query.reportId;
  if (!reportId) {
    message.error('报表ID不能为空');
    return;
  }

  try {
    const res = await OnlineReportApi.getOnlineReport(String(reportId));

    if (!res) {
      message.error('报表不存在');
      return;
    }

    reportInfo.value = res;
    reportColumns.value = res.columns || [];
    reportParams.value = res.params || [];
  } catch (error) {
    console.error('获取报表信息失败', error);
    message.error('获取报表信息失败');
  }
}

// 创建表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: [],
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const reportId = props.reportId || route.query.reportId;

          if (
            !reportId ||
            !reportInfo.value ||
            reportColumns.value.length === 0
          ) {
            return { list: [], total: 0 };
          }

          try {
            // 构建参数
            const params: OnlineReportApi.ReportParamValueDTO[] = [];
            for (const key in formValues) {
              if (formValues[key] !== undefined && formValues[key] !== '') {
                params.push({
                  paramName: key,
                  paramValue: formValues[key],
                });
              }
            }

            const requestData: any = {
              reportId: String(reportId),
              params,
            };

            // 只有当 page = 1 时才发送分页参数
            if (reportInfo.value.page === 1) {
              requestData.pageNo = page.currentPage;
              requestData.pageSize = page.pageSize;
            }
            const res = await OnlineReportApi.queryReportData(requestData);

            return {
              list: res?.rows || [],
              total: res?.total || 0,
            };
          } catch (error) {
            console.error('获取报表数据失败', error);
            message.error('获取报表数据失败');
            return { list: [], total: 0 };
          }
        },
      },
    },
    rowConfig: {
      // 不设置固定的keyField，让VXE Table自动处理
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
    pagerConfig: {
      enabled: true,
    },
  } as VxeTableGridOptions<any>,
});

onMounted(async () => {
  await getReportInfo();
  // 获取报表信息后，更新表格和表单配置
  if (reportColumns.value.length > 0) {
    const tableColumns = getTableColumns();
    // TODO 有参数无分页时数据会不显示, 暂时显示分页
    const isPaginated =
      reportInfo.value?.page === 1 || reportParams.value.length > 0;

    gridApi.setGridOptions({
      columns: tableColumns,
      pagerConfig: {
        enabled: isPaginated,
      },
    });

    // 更新表单配置
    if (reportParams.value.length > 0) {
      // 有参数时，更新表单schema并显示搜索表单
      gridApi.setState({
        formOptions: {
          schema: useGridFormSchema(),
        },
        showSearchForm: true,
      });

      // 所有查询条件都是必填的，等待用户填写后查询
    } else {
      // 没有参数，隐藏搜索表单并直接查询
      gridApi.setState({
        showSearchForm: false,
      });
      gridApi.query();
    }
  }
});
</script>

<template>
  <Page auto-content-height>
    <Grid :table-title="getTableTitle()" />
  </Page>
</template>
