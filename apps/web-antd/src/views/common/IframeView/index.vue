<template>
  <Page auto-content-height>
    <IFrame v-if="!loading" v-loading="loading" :src="src" />
  </Page>
</template>
<script lang="ts" setup>
import type { InfraConfigApi } from '#/api/infra/config';
import { useAccessStore } from '@vben/stores';
import { onMounted, ref } from 'vue';

import { useRoute } from 'vue-router'
import { Page } from "@vben/common-ui";
import { IFrame } from "#/components/iframe";

defineOptions({ name: 'CommonIframeView' })

const route = useRoute()
const loading = ref(true); // 是否加载中
const url = ref('')
const accessStore = useAccessStore();


/** 初始化 */
onMounted(async () => {
  try {
    // 检查是否存在configKey
    if (!route.name) {
      console.error('无法获取configKey参数')
      return
    }

    // 通过configKey获取URL
    const data = await InfraConfigApi.getConfigKey(route.name)
    if (data && data.length > 0) {
      // 如果需要带上token，可以添加
      url.value = data + `?token=` + accessStore.accessToken
    } else {
      // 如果配置不存在，可以设置一个默认值或者提示错误
      console.warn(`未找到配置: ${route.name}`)
    }
  } finally {
    loading.value = false
  }
})
</script>
