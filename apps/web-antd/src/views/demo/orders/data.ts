import type {
  OnActionClickParams,
  VbenFormSchema,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { OrdersApi } from '#/api/demo/orders';

import { z } from '@vben/common-ui';

import { getDictOptions } from '#/utils';

// 订单状态字典
const ORDER_STATUS_OPTIONS = [
  { label: '待支付', value: 0 },
  { label: '已支付', value: 1 },
  { label: '已发货', value: 2 },
  { label: '已完成', value: 3 },
  { label: '已取消', value: 4 },
];

// 支付方式字典
const PAY_TYPE_OPTIONS = [
  { label: '支付宝', value: 1 },
  { label: '微信', value: 2 },
  { label: '银行卡', value: 3 },
];

/** 搜索表单的字段 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'orderNo',
      label: '订单编号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入订单编号',
      },
    },
    {
      fieldName: 'userId',
      label: '用户ID',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入用户ID',
        class: 'w-full',
      },
    },
    {
      fieldName: 'status',
      label: '订单状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择订单状态',
        options: ORDER_STATUS_OPTIONS,
        allowClear: true,
      },
    },
    {
      fieldName: 'payType',
      label: '支付方式',
      component: 'Select',
      componentProps: {
        placeholder: '请选择支付方式',
        options: PAY_TYPE_OPTIONS,
        allowClear: true,
      },
    },
    {
      fieldName: 'minTotalAmount',
      label: '最小金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入最小金额',
        class: 'w-full',
        min: 0,
        precision: 2,
      },
    },
    {
      fieldName: 'maxTotalAmount',
      label: '最大金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入最大金额',
        class: 'w-full',
        min: 0,
        precision: 2,
      },
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      fieldName: 'payTime',
      label: '支付时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
  ];
}

/** 列表的字段 */
export function useGridColumns(
  onActionClick: (params: OnActionClickParams<OrdersApi.OrdersVO>) => void,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: '订单ID',
      width: 80,
    },
    {
      field: 'orderNo',
      title: '订单编号',
      width: 160,
    },
    {
      field: 'userId',
      title: '用户ID',
      width: 100,
    },
    {
      field: 'status',
      title: '订单状态',
      width: 100,
      formatter: ({ cellValue }) => {
        const option = ORDER_STATUS_OPTIONS.find(item => item.value === cellValue);
        return option?.label || '-';
      },
      cellRender: {
        name: 'CellTag',
        props: ({ row }) => {
          const colorMap = {
            0: 'orange',    // 待支付
            1: 'blue',      // 已支付
            2: 'cyan',      // 已发货
            3: 'green',     // 已完成
            4: 'red',       // 已取消
          };
          const option = ORDER_STATUS_OPTIONS.find(item => item.value === row.status);
          return {
            color: colorMap[row.status as keyof typeof colorMap] || 'default',
            text: option?.label || '-',
          };
        },
      },
    },
    {
      field: 'totalAmount',
      title: '订单总金额',
      width: 120,
      formatter: ({ cellValue }) => `¥${cellValue?.toFixed(2) || '0.00'}`,
    },
    {
      field: 'payAmount',
      title: '实付金额',
      width: 120,
      formatter: ({ cellValue }) => cellValue ? `¥${cellValue.toFixed(2)}` : '-',
    },
    {
      field: 'payType',
      title: '支付方式',
      width: 100,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        const option = PAY_TYPE_OPTIONS.find(item => item.value === cellValue);
        return option?.label || '-';
      },
    },
    {
      field: 'payTime',
      title: '支付时间',
      width: 160,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        return new Date(cellValue).toLocaleString('zh-CN');
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 160,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        return new Date(cellValue).toLocaleString('zh-CN');
      },
    },
    {
      field: 'remark',
      title: '备注',
      width: 150,
      showOverflow: 'tooltip',
    },
    {
      title: '操作',
      width: 280,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}

/** 新增/修改表单的字段 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'id',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'orderNo',
      label: '订单编号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入订单编号',
      },
      rules: 'required',
    },
    {
      fieldName: 'userId',
      label: '用户ID',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入用户ID',
        class: 'w-full',
        min: 1,
      },
      rules: z.number().min(1, '用户ID必须大于0'),
    },
    {
      fieldName: 'totalAmount',
      label: '订单总金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入订单总金额',
        class: 'w-full',
        min: 0.01,
        precision: 2,
        addonBefore: '¥',
      },
      rules: z.number().min(0.01, '订单总金额必须大于0'),
    },
    {
      fieldName: 'remark',
      label: '订单备注',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入订单备注',
        rows: 3,
        maxlength: 255,
        showCount: true,
      },
    },
  ];
}

/** 支付表单的字段 */
export function usePayFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'id',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'payAmount',
      label: '实付金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入实付金额',
        class: 'w-full',
        min: 0.01,
        precision: 2,
        addonBefore: '¥',
      },
      rules: z.number().min(0.01, '实付金额必须大于0'),
    },
    {
      fieldName: 'payType',
      label: '支付方式',
      component: 'Select',
      componentProps: {
        placeholder: '请选择支付方式',
        options: PAY_TYPE_OPTIONS,
      },
      rules: z.number().min(1, '请选择支付方式'),
    },
  ];
}

/** 更新表单的字段 */
export function useUpdateFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'id',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'status',
      label: '订单状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择订单状态',
        options: ORDER_STATUS_OPTIONS,
      },
      rules: z.number().min(0, '请选择订单状态'),
    },
    {
      fieldName: 'payAmount',
      label: '实付金额',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入实付金额',
        class: 'w-full',
        min: 0,
        precision: 2,
        addonBefore: '¥',
      },
    },
    {
      fieldName: 'payType',
      label: '支付方式',
      component: 'Select',
      componentProps: {
        placeholder: '请选择支付方式',
        options: PAY_TYPE_OPTIONS,
        allowClear: true,
      },
    },
    {
      fieldName: 'remark',
      label: '订单备注',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入订单备注',
        rows: 3,
        maxlength: 255,
        showCount: true,
      },
    },
  ];
}

export { ORDER_STATUS_OPTIONS, PAY_TYPE_OPTIONS };
