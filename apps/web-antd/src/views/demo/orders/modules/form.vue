<script lang="ts" setup>
import type { OrdersApi } from '#/api/demo/orders';

import { computed } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { createOrders, updateOrders } from '#/api/demo/orders';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

interface Props {
  formData?: null | OrdersApi.OrdersVO;
}

interface Emits {
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  formData: null,
});

const emit = defineEmits<Emits>();

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  layout: 'horizontal',
  schema: useFormSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    modalApi.lock();
    // 提交表单
    const data = (await formApi.getValues()) as OrdersApi.OrdersVO;
    try {
      await (props.formData?.id
        ? updateOrders({
            id: props.formData.id,
            status: data.status,
            payAmount: data.payAmount,
            payType: data.payType,
            remark: data.remark,
          })
        : createOrders({
            orderNo: data.orderNo,
            userId: data.userId,
            totalAmount: data.totalAmount,
            remark: data.remark,
          }));
      // 关闭并提示
      await modalApi.close();
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
    } finally {
      modalApi.unlock();
    }
  },
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      await formApi.resetForm();
      if (props.formData) {
        await formApi.setValues(props.formData);
      }
    }
  },
});

const modalTitle = computed(() => {
  return props.formData?.id
    ? $t('ui.actionTitle.edit', ['订单'])
    : $t('ui.actionTitle.create', ['订单']);
});

defineExpose({
  modalApi,
});
</script>

<template>
  <Modal :title="modalTitle" class="w-[600px]" :loading="false">
    <Form />
  </Modal>
</template>
