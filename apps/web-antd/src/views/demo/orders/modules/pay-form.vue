<script lang="ts" setup>
import type { OrdersApi } from '#/api/demo/orders';

import { computed } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { payOrders } from '#/api/demo/orders';

import { usePayFormSchema } from '../data';

interface Props {
  formData?: null | OrdersApi.OrdersVO;
}

interface Emits {
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  formData: null,
});

const emit = defineEmits<Emits>();

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  layout: 'horizontal',
  schema: usePayFormSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    modalApi.lock();
    // 提交表单
    const data = (await formApi.getValues()) as OrdersApi.OrdersVO;
    try {
      if (props.formData?.id) {
        await payOrders(props.formData.id, data.payAmount!, data.payType!);
      }
      // 关闭并提示
      await modalApi.close();
      emit('success');
      message.success('支付成功');
    } finally {
      modalApi.unlock();
    }
  },
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      await formApi.resetForm();
      if (props.formData) {
        await formApi.setValues({
          id: props.formData.id,
          payAmount: props.formData.totalAmount, // 默认实付金额等于订单总金额
        });
      }
    }
  },
});

const modalTitle = computed(() => {
  return '支付订单';
});

defineExpose({
  modalApi,
});
</script>

<template>
  <Modal :title="modalTitle" class="w-[500px]" :loading="false">
    <div class="mb-4 rounded bg-gray-50 p-4">
      <div class="mb-2 flex items-center justify-between">
        <span class="text-gray-600">订单编号：</span>
        <span class="font-medium">{{ formData?.orderNo }}</span>
      </div>
      <div class="flex items-center justify-between">
        <span class="text-gray-600">订单总金额：</span>
        <span class="font-medium text-red-500"
          >¥{{ formData?.totalAmount?.toFixed(2) }}</span
        >
      </div>
    </div>
    <Form />
  </Modal>
</template>
