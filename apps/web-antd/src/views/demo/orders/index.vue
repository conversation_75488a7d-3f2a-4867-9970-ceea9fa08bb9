<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { OrdersApi } from '#/api/demo/orders';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import {
  Download,
  IconifyIcon,
  Plus,
  SvgWalletIcon,
  Trash2,
  Upload,
  X,
} from '@vben/icons';

import {
  Upload as AntUpload,
  Button,
  InputNumber,
  message,
  Modal,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  cancelOrders,
  createOrders,
  deleteOrders,
  exportOrders,
  getOrdersImportTemplate,
  getOrdersPage,
} from '#/api/demo/orders';

import { useGridColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';
import PayForm from './modules/pay-form.vue';
import UpdateForm from './modules/update-form.vue';

defineOptions({ name: 'DemoOrders' });

// 表单弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

// 支付弹窗
const [PayFormModal, payFormModalApi] = useVbenModal({
  connectedComponent: PayForm,
  destroyOnClose: true,
});

// 更新弹窗
const [UpdateFormModal, updateFormModalApi] = useVbenModal({
  connectedComponent: UpdateForm,
  destroyOnClose: true,
});

// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          // 处理搜索参数
          const params: OrdersApi.OrdersQueryDTO = {
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          // 金额范围已经在表单中分别处理，无需额外处理

          // 处理时间范围
          if (formValues.createTime && Array.isArray(formValues.createTime)) {
            params.createTime = formValues.createTime
              .map((date: any) => (date ? new Date(date).getTime() : null))
              .filter(Boolean);
          }

          if (formValues.payTime && Array.isArray(formValues.payTime)) {
            params.payTime = formValues.payTime
              .map((date: any) => (date ? new Date(date).getTime() : null))
              .filter(Boolean);
          }

          return await getOrdersPage(params);
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<OrdersApi.OrdersVO>,
});

/** 表格操作按钮的回调函数 */
function onActionClick({ code, row }: OnActionClickParams<OrdersApi.OrdersVO>) {
  switch (code) {
    case 'cancel': {
      onCancel(row);
      break;
    }
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
    case 'pay': {
      onPay(row);
      break;
    }
    case 'update': {
      onUpdate(row);
      break;
    }
  }
}

/** 新增 */
function onCreate() {
  formModalApi.open({
    formData: null,
    onSuccess: onRefresh,
  });
}

/** 编辑 */
function onEdit(row: OrdersApi.OrdersVO) {
  formModalApi.open({
    formData: row,
    onSuccess: onRefresh,
  });
}

/** 更新 */
function onUpdate(row: OrdersApi.OrdersVO) {
  updateFormModalApi.open({
    formData: row,
    onSuccess: onRefresh,
  });
}

/** 支付 */
function onPay(row: OrdersApi.OrdersVO) {
  if (row.status !== 0) {
    message.warning('只有待支付的订单才能支付');
    return;
  }
  payFormModalApi.open({
    formData: row,
    onSuccess: onRefresh,
  });
}

/** 取消订单 */
function onCancel(row: OrdersApi.OrdersVO) {
  if (row.status !== 0) {
    message.warning('只有待支付的订单才能取消');
    return;
  }

  Modal.confirm({
    title: '确认取消',
    content: `确定要取消订单 "${row.orderNo}" 吗？`,
    onOk: async () => {
      try {
        await cancelOrders(row.id!);
        message.success('取消成功');
        onRefresh();
      } catch (error) {
        console.error('取消失败:', error);
      }
    },
  });
}

/** 删除 */
function onDelete(row: OrdersApi.OrdersVO) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除订单 "${row.orderNo}" 吗？`,
    onOk: async () => {
      try {
        await deleteOrders(row.id!);
        message.success('删除成功');
        onRefresh();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
}

/** 导出 */
async function onExport() {
  try {
    const formValues = await gridApi.getFormValues();
    const params: OrdersApi.OrdersExportDTO = {
      pageNo: 1,
      pageSize: 10_000,
      ...formValues,
    };

    // 金额范围已经在表单中分别处理，无需额外处理

    // 处理时间范围
    if (formValues.createTime && Array.isArray(formValues.createTime)) {
      params.createTime = formValues.createTime
        .map((date: any) => (date ? new Date(date).getTime() : null))
        .filter(Boolean);
    }

    if (formValues.payTime && Array.isArray(formValues.payTime)) {
      params.payTime = formValues.payTime
        .map((date: any) => (date ? new Date(date).getTime() : null))
        .filter(Boolean);
    }

    await exportOrders(params);
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}

/** 下载导入模板 */
async function onDownloadTemplate() {
  try {
    await getOrdersImportTemplate();
    message.success('模板下载成功');
  } catch (error) {
    console.error('模板下载失败:', error);
    message.error('模板下载失败');
  }
}

/** 导入 */
function onImport(info: any) {
  const { file } = info;

  if (file.status === 'done') {
    if (file.response?.code === 0) {
      message.success('导入成功');
      onRefresh();
    } else {
      message.error(file.response?.msg || '导入失败');
    }
  } else if (file.status === 'error') {
    message.error('导入失败');
  }
}

/** 刷新 */
function onRefresh() {
  gridApi.query();
}

// 批量生成数据的状态
const batchGenerateCount = ref(10);
const isGenerating = ref(false);

/** 生成随机订单数据 */
function generateRandomOrderData(): OrdersApi.OrdersAddDTO {
  const timestamp = Date.now();
  const randomId = Math.floor(Math.random() * 10_000);

  return {
    orderNo: `ORD${timestamp}${randomId}`,
    userId: Math.floor(Math.random() * 1000) + 1,
    totalAmount: Math.floor(Math.random() * 10_000) / 100 + 1, // 1-100元随机金额
    remark: `测试订单 - ${new Date().toLocaleString()}`,
  };
}

/** 批量生成订单数据 */
async function onBatchGenerate() {
  if (batchGenerateCount.value <= 0) {
    message.warning('请输入有效的生成数量');
    return;
  }

  if (batchGenerateCount.value > 100_000) {
    message.warning('单次生成数量不能超过100000条');
    return;
  }

  Modal.confirm({
    title: '确认批量生成',
    content: `确定要生成 ${batchGenerateCount.value} 条测试订单数据吗？`,
    onOk: async () => {
      isGenerating.value = true;
      let successCount = 0;
      let failCount = 0;

      try {
        const hideLoading = message.loading({
          content: `正在生成订单数据... (0/${batchGenerateCount.value})`,
          duration: 0,
          key: 'batch_generate_msg',
        });

        // 批量创建订单
        const promises = [];
        for (let i = 0; i < batchGenerateCount.value; i++) {
          const orderData = generateRandomOrderData();
          promises.push(
            createOrders(orderData)
              .then(() => {
                successCount++;
                message.loading({
                  content: `正在生成订单数据... (${successCount + failCount}/${batchGenerateCount.value})`,
                  duration: 0,
                  key: 'batch_generate_msg',
                });
              })
              .catch(() => {
                failCount++;
                message.loading({
                  content: `正在生成订单数据... (${successCount + failCount}/${batchGenerateCount.value})`,
                  duration: 0,
                  key: 'batch_generate_msg',
                });
              }),
          );

          // 每10个请求暂停一下，避免请求过于频繁
          if ((i + 1) % 10 === 0) {
            await Promise.all(promises.splice(0, 10));
            await new Promise((resolve) => setTimeout(resolve, 1));
          }
        }

        // 等待剩余的请求完成
        if (promises.length > 0) {
          await Promise.all(promises);
        }

        hideLoading();

        if (failCount === 0) {
          message.success(`成功生成 ${successCount} 条订单数据`);
        } else {
          message.warning(
            `生成完成：成功 ${successCount} 条，失败 ${failCount} 条`,
          );
        }

        // 刷新表格
        onRefresh();
      } catch (error) {
        console.error('批量生成失败:', error);
        message.error('批量生成失败');
      } finally {
        isGenerating.value = false;
      }
    },
  });
}
</script>

<template>
  <Page auto-content-height>
    <Grid table-title="订单管理">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增订单
        </Button>
        <Button @click="onExport">
          <Download class="size-5" />
          导出
        </Button>
        <Button @click="onDownloadTemplate">
          <Download class="size-5" />
          下载模板
        </Button>
        <AntUpload
          action="/demo/orders/import"
          :show-upload-list="false"
          accept=".xlsx,.xls"
          @change="onImport"
        >
          <Button>
            <Upload class="size-5" />
            导入
          </Button>
        </AntUpload>

        <!-- 批量生成数据 -->
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600">批量生成:</span>
          <InputNumber
            v-model:value="batchGenerateCount"
            :min="1"
            :max="100000"
            :step="10"
            size="small"
            style="width: 80px"
            placeholder="数量"
          />
          <Button
            type="dashed"
            :loading="isGenerating"
            @click="onBatchGenerate"
          >
            <IconifyIcon icon="lucide:database" class="size-4" />
            生成测试数据
          </Button>
        </div>
      </template>

      <template #actions="{ row }">
        <Button
          v-if="row.status === 0"
          type="primary"
          size="small"
          @click="onActionClick({ code: 'pay', row })"
        >
          <SvgWalletIcon class="size-4" />
          支付
        </Button>
        <Button size="small" @click="onActionClick({ code: 'update', row })">
          <IconifyIcon icon="lucide:edit" class="size-4" />
          更新
        </Button>
        <Button
          v-if="row.status === 0"
          type="default"
          size="small"
          @click="onActionClick({ code: 'cancel', row })"
        >
          <X class="size-4" />
          取消
        </Button>
        <Button
          type="primary"
          danger
          size="small"
          @click="onActionClick({ code: 'delete', row })"
        >
          <Trash2 class="size-4" />
          删除
        </Button>
      </template>
    </Grid>

    <!-- 表单弹窗 -->
    <FormModal @success="onRefresh" />

    <!-- 支付弹窗 -->
    <PayFormModal @success="onRefresh" />

    <!-- 更新弹窗 -->
    <UpdateFormModal @success="onRefresh" />
  </Page>
</template>
