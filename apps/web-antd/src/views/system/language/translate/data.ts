import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemLanguageApi } from '#/api/system/language/language';

/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'langKey',
      label: '语言键',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请输入语言键',
      },
    },
    {
      fieldName: 'langTranslate',
      label: '翻译内容',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请输入翻译内容',
      },
    },
  ];
}

/** 列表的基础字段 */
export function useGridColumns(languages: SystemLanguageApi.Language[] = []): VxeTableGridOptions['columns'] {
  const columns: VxeTableGridOptions['columns'] = [
    {
      field: 'langKey',
      title: '语言键',
      minWidth: 200,
    },
  ];
  
  // 添加动态语言列
  languages.forEach(lang => {
    columns.push({
      field: lang.langCode,
      title: lang.langName,
      minWidth: 200,
      slots: { default: `cell-${lang.langCode}` },
    });
  });
  
  // 添加固定列
  columns.push(
    {
      field: 'createTime',
      title: '创建时间',
      formatter: 'formatDateTime',
      width: 180,
    },
    {
      title: '操作',
      width: 150,
      fixed: 'right',
      slots: { default: 'actions' },
    }
  );
  
  return columns;
}

/** 表单配置 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'id',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'langKey',
      label: '语言键',
      component: 'Input',
      componentProps: {
        placeholder: '请输入语言键',
      },
      rules: 'required',
    },
    // 动态语言翻译表单项会在组件中添加
  ];
} 