<script lang="ts" setup>
import type { SystemLanguageTranslateApi } from '#/api/system/language/translate/translate';
import type { SystemLanguageApi } from '#/api/system/language/language';

import { computed, ref, onMounted } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';
import { Divider, Form as AForm, Input } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addTranslate, updateTranslate } from '#/api/system/language/translate/translate';
import { getLanguageAll } from '#/api/system/language/language';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emit = defineEmits(['success']);
const formData = ref<SystemLanguageTranslateApi.Translate>();
const getTitle = computed(() =>
  formData.value?.id
    ? $t('ui.actionTitle.edit', ['翻译'])
    : $t('ui.actionTitle.create', ['翻译']),
);

// 语言列表
const languageList = ref<SystemLanguageApi.Language[]>([]);
// 翻译内容
const translations = ref<Record<string, string>>({});

// 获取语言列表
const fetchLanguages = async () => {
  try {
    const data = await getLanguageAll();
    if (data) {
      // 只显示启用的语言
      languageList.value = data.filter((item: SystemLanguageApi.Language) => item.enableFlag === 1);
    }
  } catch (error) {
    console.error('获取语言列表失败:', error);
    message.error('获取语言列表失败');
  }
};

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  layout: 'horizontal',
  schema: useFormSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    modalApi.lock();
    
    // 提交表单
    const values = await formApi.getValues();
    // 合并表单值和手动输入的翻译
    const data: SystemLanguageTranslateApi.Translate = {
      id: formData.value?.id,
      langKey: values.langKey,
      langTranslate: JSON.stringify(translations.value || {})
    };
    
    try {
      await (formData.value?.id ? updateTranslate(data) : addTranslate(data));
      // 关闭并提示
      await modalApi.close();
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
    } finally {
      modalApi.unlock();
    }
  },
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      formData.value = undefined;
      translations.value = {};
      return;
    }
    
    // 确保语言列表已加载
    if (languageList.value.length === 0) {
      await fetchLanguages();
    }
    
    // 加载数据
    const data = modalApi.getData<SystemLanguageTranslateApi.Translate>();
    if (!data) {
      // 新建时初始化空的翻译对象
      await formApi.setValues({
        langKey: '',
      });
      translations.value = {};
      return;
    }
    
    formData.value = data;
    
    // 解析翻译JSON
    try {
      translations.value = data.langTranslate ? JSON.parse(data.langTranslate) : {};
    } catch (e) {
      console.error('解析翻译JSON失败:', e);
      translations.value = {};
    }
    
    // 设置表单值
    await formApi.setValues({
      id: data.id,
      langKey: data.langKey,
    });
  },
});

onMounted(() => {
  fetchLanguages();
});
</script>

<template>
  <Modal :title="getTitle" class="w-[700px]">
    <Form class="mx-4" />
    <div class="translations-form" v-if="languageList.length > 0">
      <Divider>{{ $t('各语言翻译') }}</Divider>
      <AForm layout="vertical">
        <AForm.Item
          v-for="lang in languageList"
          :key="lang.langCode"
          :label="lang.langName"
        >
          <Input.TextArea
            v-model:value="translations[lang.langCode]"
            :rows="3"
            :maxlength="5000"
            :placeholder="`请输入${lang.langName}翻译`"
            show-count
          />
        </AForm.Item>
      </AForm>
    </div>
  </Modal>
</template>

<style scoped>
.translations-form {
  max-height: 500px;
  overflow-y: auto;
  padding: 0 20px;
}
</style> 