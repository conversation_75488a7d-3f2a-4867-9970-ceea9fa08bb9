<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemLanguageTranslateApi } from '#/api/system/language/translate/translate';
import type { SystemLanguageApi } from '#/api/system/language/language';

import { ref, onMounted, watch, reactive } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromBlobPart } from '@vben/utils';

import { message, Upload, Tooltip } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteTranslate, exportTranslateExcel, getTranslatePage, importTranslateExcel } from '#/api/system/language/translate/translate';
import { getLanguageAll } from '#/api/system/language/language';
import { $t } from '#/locales';

import { useGridFormSchema } from './data';
import Form from './modules/form.vue';

defineOptions({ name: 'SystemLanguageTranslate' });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

// 语言列表
const languages = ref<SystemLanguageApi.Language[]>([]);
// 导出加载状态
const exportLoading = ref(false);
// 表格列配置
const columns = reactive<any[]>([
  {
    field: 'langKey',
    title: '语言键',
    minWidth: 200,
  },
  {
    field: 'createTime',
    title: '创建时间',
    formatter: 'formatDateTime',
    width: 180,
  },
  {
    title: '操作',
    width: 150,
    fixed: 'right',
    slots: { default: 'actions' },
  },
]);

// 获取语言列表
const fetchLanguages = async () => {
  try {
    const data = await getLanguageAll();
    if (data) {
      // 只显示启用的语言
      languages.value = data.filter((item: SystemLanguageApi.Language) => item.enableFlag === 1);
      console.log('获取到的语言列表:', languages.value);
      
      // 更新表格列配置
      updateTableColumns();
    }
  } catch (error) {
    console.error('获取语言列表失败:', error);
    message.error('获取语言列表失败');
  }
};

// 从翻译JSON字符串中获取对应语言的翻译
const getTranslateValue = (translateJson: string, langCode: string) => {
  if (!translateJson) return '';
  try {
    const translateObj = JSON.parse(translateJson);
    return translateObj[langCode] || '';
  } catch (e) {
    console.error('解析翻译JSON失败:', e);
    return '';
  }
};

// 更新表格列配置
const updateTableColumns = () => {
  // 清除旧的语言列
  columns.splice(1, columns.length - 3);
  
  // 添加新的语言列
  languages.value.forEach(lang => {
    const currentLangCode = lang.langCode; // 创建闭包变量
    columns.splice(columns.length - 2, 0, {
      field: 'langTranslate',
      title: lang.langName,
      minWidth: 200,
      formatter: ({ cellValue }: any) => {
        if (!cellValue) return '';
        return getTranslateValue(cellValue, currentLangCode);
      }
    });
  });
};

// 格式化翻译内容，限制显示长度
const formatTranslateContent = (translateJson: string, langCode: string) => {
  const content = getTranslateValue(translateJson, langCode);
  const maxLength = 50;
  if (content.length <= maxLength) {
    return content;
  }
  return content.substring(0, maxLength) + '...';
};

// 判断是否需要显示完整内容的提示
const shouldShowTooltip = (translateJson: string, langCode: string) => {
  const content = getTranslateValue(translateJson, langCode);
  return content.length > 50;
};

/** 刷新表格 */
function onRefresh() {
  gridApi?.query();
}

/** 创建翻译 */
function handleAdd() {
  formModalApi.setData({
    langKey: '',
    langTranslate: '{}'
  }).open();
}

/** 编辑翻译 */
function handleUpdate(row: SystemLanguageTranslateApi.Translate) {
  formModalApi.setData(row).open();
}

/** 删除翻译 */
async function handleDelete(ids?: number[]) {
  if (!ids || ids.length === 0) {
    message.warning('请选择要删除的数据');
    return;
  }
  
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', ['翻译']),
    key: 'action_key_msg',
  });
  try {
    await deleteTranslate(ids);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess'),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 导出Excel */
async function handleExport() {
  try {
    exportLoading.value = true;
    const data = await exportTranslateExcel(await gridApi.formApi.getValues());
    downloadFileFromBlobPart({ fileName: '翻译数据.xlsx', source: data });
    message.success('导出成功');
  } finally {
    exportLoading.value = false;
  }
}

/** 导入Excel前检查 */
function handleBeforeUpload(file: File) {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                 file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件!');
    return false;
  }
  return true;
}

/** 导入Excel */
async function handleImport(options: any) {
  const { file } = options;
  try {
    const hideLoading = message.loading({
      content: '正在导入...',
      key: 'import_loading',
    });
    await importTranslateExcel(file);
    message.success({
      content: '导入成功',
      key: 'import_loading',
    });
    onRefresh();
  } catch (error) {
    console.error('导入失败:', error);
    message.error('导入失败');
  }
}

// 创建表格组件和API
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: columns,
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getTranslatePage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<SystemLanguageTranslateApi.Translate>,
});

// 监听语言列表变化，重新刷新表格
watch(languages, () => {
  updateTableColumns();
  onRefresh();
}, { deep: true });

onMounted(() => {
  fetchLanguages();
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid table-title="语言翻译列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create'),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              onClick: handleAdd,
            },
            {
              label: $t('ui.actionTitle.export'),
              type: 'primary',
              icon: ACTION_ICON.DOWNLOAD,
              onClick: handleExport,
              loading: exportLoading,
            },
          ]"
        />
        <Upload
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :custom-request="handleImport"
        >
          <TableAction
            :actions="[
              {
                label: $t('ui.actionTitle.import'),
                type: 'primary',
                icon: ACTION_ICON.UPLOAD,
              },
            ]"
          />
        </Upload>
      </template>
      
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('common.edit'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              onClick: () => handleUpdate(row),
              ifShow: true,
            },
            {
              label: $t('common.delete'),
              type: 'link',
              icon: ACTION_ICON.DELETE,
              danger: true,
              ifShow: true,
              onClick: () => handleDelete([row.id]),
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template> 