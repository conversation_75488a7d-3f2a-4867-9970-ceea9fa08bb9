<script lang="ts" setup>
import type { SystemLanguageApi } from '#/api/system/language/language';

import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addLanguage, updateLanguage } from '#/api/system/language/language';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emit = defineEmits(['success']);
const formData = ref<SystemLanguageApi.Language>();
const isEdit = computed(() => !!formData.value?.langCode);
const getTitle = computed(() => 
  isEdit.value 
    ? $t('ui.actionTitle.edit', ['语言']) 
    : $t('ui.actionTitle.create', ['语言'])
);

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
    labelWidth: 100,
  },
  layout: 'horizontal',
  schema: useFormSchema(),
  showDefaultActions: false,
});

watch(isEdit, (val) => {
  formApi.updateSchema([
    {
      fieldName: 'langCode',
      componentProps: {
        disabled: val
      }
    }
  ]);
}, { immediate: true });

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    
    modalApi.lock();
    
    try {
      // 获取表单数据
      const formValues = await formApi.getValues() as SystemLanguageApi.Language;
      
      // 根据是否编辑模式决定调用哪个API
      if (isEdit.value) {
        await updateLanguage(formValues);
      } else {
        await addLanguage(formValues);
      }
      
      // 关闭弹窗并提示成功
      await modalApi.close();
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
    } catch (error) {
      console.error('保存语言失败:', error);
      message.error('保存失败，请重试');
    } finally {
      modalApi.unlock();
    }
  },
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      // 关闭弹窗时清空数据
      formData.value = undefined;
      return;
    }
    
    // 获取传入的数据
    const data = modalApi.getData<SystemLanguageApi.Language>();
    formData.value = data;
    
    // 设置表单值
    if (data) {
      await formApi.setValues({
        id: data.id,
        langCode: data.langCode || '',
        langName: data.langName || '',
        enableFlag: data.enableFlag ?? 1,
        defaultFlag: data.defaultFlag ?? 0
      });
      
      // 如果是编辑模式，禁用语言编码输入框
      if (data.langCode) {
        formApi.updateSchema([
          {
            fieldName: 'langCode',
            componentProps: {
              disabled: true
            }
          }
        ]);
      }
    } else {
      // 新建模式，设置默认值
      await formApi.setValues({
        langCode: '',
        langName: '',
        enableFlag: 1,
        defaultFlag: 0
      });
      
      // 新建模式，启用语言编码输入框
      formApi.updateSchema([
        {
          fieldName: 'langCode',
          componentProps: {
            disabled: false
          }
        }
      ]);
    }
  },
});
</script>

<template>
  <Modal :title="getTitle" class="w-[500px]">
    <Form class="mx-4" />
  </Modal>
</template> 