<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemLanguageApi } from '#/api/system/language/language';

import { Page, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteLanguage, getLanguagePage, updateLanguage } from '#/api/system/language/language';
import { $t } from '#/locales';

import { useGridColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

defineOptions({ name: 'SystemLanguage' });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 创建语言 */
function handleCreate() {
  formModalApi.setData({
    id: undefined,
    langCode: '',
    langName: '',
    enableFlag: 1,
    defaultFlag: 0
  }).open();
}

/** 编辑语言 */
function handleEdit(row: SystemLanguageApi.Language) {
  formModalApi.setData({
    ...row
  }).open();
}

/** 删除语言 */
async function handleDelete(row: SystemLanguageApi.Language) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.langName]),
    key: 'action_key_msg',
  });
  try {
    await deleteLanguage([row.id]);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.langName]),
      key: 'action_key_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

/** 切换语言状态 */
async function handleToggleStatus(row: SystemLanguageApi.Language) {
  const newStatus = row.enableFlag === 1 ? 0 : 1;
  const actionText = newStatus === 1 ? '启用' : '禁用';
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.processing', [actionText]),
    key: 'action_process_msg',
  });
  try {
    await updateLanguage({
      ...row,
      enableFlag: newStatus
    });
    message.success({
      content: $t('ui.actionMessage.operationSuccess'),
      key: 'action_process_msg',
    });
    onRefresh();
  } finally {
    hideLoading();
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getLanguagePage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<SystemLanguageApi.Language>,
});
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid table-title="语言管理列表">
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create'),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              onClick: handleCreate,
            },
          ]"
        />
      </template>
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: $t('编辑'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              onClick: handleEdit.bind(null, row),
              ifShow: true,
            },
            {
              label: row.enableFlag === 1 ? '禁用' : '启用',
              type: 'link',
              icon: ACTION_ICON.EDIT,
              ifShow: row.defaultFlag !== 1,
              onClick: handleToggleStatus.bind(null, row),
            },
            {
              label: $t('删除'),
              type: 'link',
              icon: ACTION_ICON.DELETE,
              danger: true,
              ifShow: row.defaultFlag !== 1,
              onClick: handleDelete.bind(null, row),
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template> 