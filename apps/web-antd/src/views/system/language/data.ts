import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { DICT_TYPE } from '#/utils/dict';

/** 列表的搜索表单 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'langCode',
      label: '语言编码',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请输入语言编码',
      },
    },
    {
      fieldName: 'langName',
      label: '语言名称',
      component: 'Input',
      componentProps: {
        allowClear: true,
        placeholder: '请输入语言名称',
      },
    },
    {
      fieldName: 'enableFlag',
      label: '状态',
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
        placeholder: '请选择状态',
      },
    },
  ];
}

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'langCode',
      title: '语言编码',
    },
    {
      field: 'langName',
      title: '语言名称',
    },
    {
      field: 'enableFlag',
      title: '状态',
      cellRender: {
        name: 'CellDict',
        props: { type: DICT_TYPE.COMMON_STATUS_YES_NO },
      },
    },
    {
      field: 'defaultFlag',
      title: '是否默认',
      cellRender: {
        name: 'CellDict',
        props: { type: DICT_TYPE.COMMON_STATUS_YES_NO },
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      formatter: 'formatDateTime',
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}

/** 表单配置 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'id',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'langCode',
      label: '语言编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入语言编码',
      },
      rules: 'required',
    },
    {
      fieldName: 'langName',
      label: '语言名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入语言名称',
      },
      rules: 'required',
    },
    {
      fieldName: 'enableFlag',
      label: '状态',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
      defaultValue: 1,
      rules: 'required',
    },
    {
      fieldName: 'defaultFlag',
      label: '是否默认',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
      defaultValue: 0,
      rules: 'required',
    },
  ];
} 