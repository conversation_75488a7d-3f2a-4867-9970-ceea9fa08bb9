<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { h } from 'vue';

import { confirm, Page } from '@vben/common-ui';
import { formatDateTime } from '@vben/utils';

import { message, Modal } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import * as OnlineUserApi from '#/api/system/user/onlineUser';
import { $t } from '#/locales';

defineOptions({ name: 'SystemOnlineUser' });

/** 刷新表格 */
function onRefresh() {
  gridApi.query();
}

/** 踢用户下线 */
async function handleKick(row: OnlineUserApi.OnlineUserRespVO) {
  const hideLoading = message.loading({
    content: $t('处理中'),
    key: 'action_key_msg',
  });
  try {
    await confirm({
      content: `确认要将"${row.username}"踢下线吗?`,
    });

    await OnlineUserApi.kickUser(row.userId);
    message.success({
      content: $t('操作成功'),
      key: 'action_key_msg',
    });
    onRefresh();
  } catch {
    // 用户取消操作，不做处理
  } finally {
    hideLoading();
  }
}

/** 封禁用户 */
async function handleDisable(row: OnlineUserApi.OnlineUserRespVO) {
  try {
    // 提示输入封禁时长
    const result = await new Promise<{ value: string }>((resolve, reject) => {
      Modal.confirm({
        title: '温馨提示',
        content: h('div', [
          h('p', `请输入封禁"${row.username}"的时长(单位：小时)`),
          h('input', {
            type: 'number',
            value: '1',
            style: 'width: 100%',
            onInput: (e: Event) => {
              (result as any).value = (e.target as HTMLInputElement).value;
            },
          }),
        ]),
        onOk: () => {
          resolve({ value: (result as any).value || '1' });
        },
        onCancel: () => {
          reject();
        },
      });

      const result: any = { value: '1' };
    });

    // 转换为秒
    const disableTime = Number.parseInt(result.value) * 3600;
    if (isNaN(disableTime) || disableTime <= 0) {
      message.error('请输入有效的封禁时长');
      return;
    }

    const hideLoading = message.loading({
      content: $t('处理中'),
      key: 'action_key_msg',
    });

    try {
      await OnlineUserApi.disableUser(row.userId, disableTime);
      message.success({
        content: `封禁成功，封禁时长：${result.value}小时`,
        key: 'action_key_msg',
      });
      onRefresh();
    } finally {
      hideLoading();
    }
  } catch {
    // 用户取消操作，不做处理
  }
}

/** 格式化在线时长 */
function onlineTimeFormatter(row: OnlineUserApi.OnlineUserRespVO) {
  const hours = Math.floor(row.onlineTime / 1000 / 3600);
  const minutes = Math.floor(((row.onlineTime / 1000) % 3600) / 60);
  const seconds = Math.floor((row.onlineTime / 1000) % 60);

  if (hours > 0) {
    return `${hours}小时${minutes}分${seconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
}

/** 使用表格组件 */
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: [
      {
        fieldName: 'username',
        label: '用户名称',
        component: 'Input',
        componentProps: {
          placeholder: '请输入用户名称',
        },
      },
      {
        fieldName: 'clientIp',
        label: 'IP地址',
        component: 'Input',
        componentProps: {
          placeholder: '请输入IP地址',
        },
      },
    ],
  },
  gridOptions: {
    columns: [
      {
        field: 'userId',
        title: '用户ID',
        align: 'center',
      },
      {
        field: 'username',
        title: '用户名称',
        align: 'center',
      },
      {
        field: 'clientIp',
        title: 'IP地址',
        align: 'center',
      },
      {
        field: 'loginTime',
        title: '登录时间',
        align: 'center',
        formatter: ({ cellValue }) => formatDateTime(cellValue),
      },
      {
        field: 'onlineTime',
        title: '在线时长',
        align: 'center',
        formatter: ({ row }) => onlineTimeFormatter(row),
      },
      {
        title: '操作',
        width: 180,
        fixed: 'right',
        slots: { default: 'actions' },
      },
    ],
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: ({ page }, formValues) => {
          return OnlineUserApi.getOnlineUserPage({
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'userId',
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<OnlineUserApi.OnlineUserRespVO>,
});
</script>

<template>
  <Page auto-content-height>
    <Grid table-title="在线用户列表">
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: '踢下线',
              type: 'link',
              icon: ACTION_ICON.DELETE,
              auth: ['system:online-user:kick'],
              onClick: handleKick.bind(null, row),
            },
            {
              label: '封禁',
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              auth: ['system:online-user:disable'],
              onClick: handleDisable.bind(null, row),
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>
