<script lang="ts" setup>
import type { SystemUserApi } from '#/api/system/user';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { formatDate } from '@vben/utils';

import {
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  Empty,
  Input,
  message,
  Select,
  Tag,
} from 'ant-design-vue';

import { getSimpleUserList } from '#/api/system/user';
import { webSocketService } from '#/utils';

// 使用WebSocket服务
const server = webSocketService.server;
const status = webSocketService.status;
const getIsOpen = webSocketService.getIsOpen;

const getTagColor = computed(() => (getIsOpen.value ? 'success' : 'red')); // WebSocket 连接的展示颜色
const getStatusText = computed(() => (getIsOpen.value ? '已连接' : '未连接')); // 连接状态文本
const connectionInfo = computed(() => {
  if (getIsOpen.value) {
    return '已自动连接到WebSocket服务器';
  } else {
    return '等待自动连接到WebSocket服务器...';
  }
});

/** 监听接收到的数据 */
const messageList = ref(
  [] as { text: string; time: number; type?: string; userId?: string }[],
); // 消息列表
const messageReverseList = computed(() => [...messageList.value].reverse());

// 注册消息处理器
function setupMessageHandlers() {
  // 处理demo-message-receive类型消息
  webSocketService.registerMessageHandler('demo-message-receive', (content) => {
    const single = content.single;
    messageList.value.push({
      text: content.text,
      time: Date.now(),
      type: single ? 'single' : 'group',
      userId: content.fromUserId,
    });
  });

  // 处理notice-push类型消息
  webSocketService.registerMessageHandler('notice-push', (content) => {
    messageList.value.push({
      text: content.title,
      time: Date.now(),
      type: 'system',
    });
  });
}

// 移除消息处理器
function removeMessageHandlers() {
  webSocketService.removeMessageHandler('demo-message-receive');
  webSocketService.removeMessageHandler('notice-push');
}

/** 发送消息 */
const sendText = ref(''); // 发送内容
const sendUserId = ref(''); // 发送人
function handlerSend() {
  if (!sendText.value.trim()) {
    message.warning('消息内容不能为空');
    return;
  }

  // 发送消息
  webSocketService.sendMessage('demo-message-send', {
    text: sendText.value,
    toUserId: sendUserId.value,
  });

  sendText.value = '';
}

/** 获取消息类型的徽标颜色 */
function getMessageBadgeColor(type?: string) {
  switch (type) {
    case 'group': {
      return 'green';
    }
    case 'single': {
      return 'blue';
    }
    case 'system': {
      return 'red';
    }
    default: {
      return 'default';
    }
  }
}

/** 获取消息类型的文本 */
function getMessageTypeText(type?: string) {
  switch (type) {
    case 'group': {
      return '群发';
    }
    case 'single': {
      return '单发';
    }
    case 'system': {
      return '系统';
    }
    default: {
      return '未知';
    }
  }
}

/** 初始化 */
const userList = ref<SystemUserApi.User[]>([]); // 用户列表
onMounted(async () => {
  userList.value = await getSimpleUserList();
  setupMessageHandlers();

  // 不在这里主动连接，让WebSocketService根据登录状态自动管理连接
  console.log('[WebSocket组件] 已初始化消息处理器');
});

// 组件卸载时移除消息处理器
onUnmounted(() => {
  removeMessageHandlers();
  console.log('[WebSocket组件] 已移除消息处理器');
});
</script>

<template>
  <Page>
    <div class="mt-4 flex flex-col gap-4 md:flex-row">
      <!-- 左侧：建立连接、发送消息 -->
      <Card :bordered="false" class="w-full md:w-1/2">
        <template #title>
          <div class="flex items-center">
            <Badge :status="getIsOpen ? 'success' : 'error'" />
            <span class="ml-2 text-lg font-medium">连接管理</span>
          </div>
        </template>
        <div class="mb-4 flex items-center rounded-lg p-3">
          <span class="mr-4 font-medium">连接状态:</span>
          <Tag :color="getTagColor" class="px-3 py-1">{{ getStatusText }}</Tag>
          <span class="ml-3 text-gray-500">{{ connectionInfo }}</span>
        </div>
        <div class="mb-6">
          <Input
            v-model:value="server"
            disabled
            class="rounded-md"
            size="large"
          >
            <template #addonBefore>
              <span class="text-gray-600">服务地址</span>
            </template>
          </Input>
          <div class="mt-2 text-gray-500 text-sm">
            <span class="i-ant-design:info-circle-outlined mr-1"></span>
            WebSocket连接会在用户登录后自动创建，登出后自动断开
          </div>
        </div>

        <Divider>
          <span class="text-gray-500">消息发送</span>
        </Divider>

        <Select
          v-model:value="sendUserId"
          class="mb-3 w-full"
          size="large"
          placeholder="请选择接收人"
          :disabled="!getIsOpen"
        >
          <Select.Option key="" value="" label="所有人">
            <div class="flex items-center">
              <Avatar size="small" class="mr-2">全</Avatar>
              <span>所有人</span>
            </div>
          </Select.Option>
          <Select.Option
            v-for="user in userList"
            :key="user.id"
            :value="user.id"
            :label="user.nickname"
          >
            <div class="flex items-center">
              <Avatar size="small" class="mr-2">
                {{ user.nickname.slice(0, 1) }}
              </Avatar>
              <span>{{ user.nickname }}</span>
            </div>
          </Select.Option>
        </Select>

        <Input.TextArea
          v-model:value="sendText"
          :auto-size="{ minRows: 3, maxRows: 6 }"
          :disabled="!getIsOpen"
          class="border-1 rounded-lg"
          allow-clear
          placeholder="请输入你要发送的消息..."
        />

        <Button
          :disabled="!getIsOpen"
          block
          class="mt-4"
          type="primary"
          size="large"
          @click="handlerSend"
        >
          <template #icon>
            <span class="i-ant-design:send-outlined mr-1"></span>
          </template>
          发送消息
        </Button>
      </Card>

      <!-- 右侧：消息记录 -->
      <Card :bordered="false" class="w-full md:w-1/2">
        <template #title>
          <div class="flex items-center">
            <span class="i-ant-design:message-outlined mr-2 text-lg"></span>
            <span class="text-lg font-medium">消息记录</span>
            <Tag v-if="messageList.length > 0" class="ml-2">
              {{ messageList.length }} 条
            </Tag>
          </div>
        </template>
        <div class="h-96 overflow-auto rounded-lg p-2">
          <Empty v-if="messageList.length === 0" description="暂无消息记录" />
          <div v-else class="space-y-3">
            <div
              v-for="msg in messageReverseList"
              :key="msg.time"
              class="rounded-lg p-3 shadow-sm"
            >
              <div class="mb-1 flex items-center justify-between">
                <div class="flex items-center">
                  <Badge :color="getMessageBadgeColor(msg.type)" />
                  <span class="ml-1 font-medium text-gray-600">{{
                    getMessageTypeText(msg.type)
                  }}</span>
                  <span v-if="msg.userId" class="ml-2 text-gray-500">
                    用户 ID: {{ msg.userId }}
                  </span>
                </div>
                <span class="text-xs text-gray-400">{{
                  formatDate(msg.time)
                }}</span>
              </div>
              <div class="mt-2 break-words text-gray-800">
                {{ msg.text }}
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  </Page>
</template>
