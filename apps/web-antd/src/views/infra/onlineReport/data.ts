import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { DICT_TYPE } from '#/utils';

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'reportId',
      title: '报表Id',
      width: 200,
    },
    {
      field: 'name',
      title: '报表名称',
      minWidth: 120,
    },
    {
      field: 'code',
      title: '报表编码',
      minWidth: 120,
    },
    {
      field: 'sqlContent',
      title: '报表SQL',
      minWidth: 150,
      showOverflow: true,
    },
    {
      field: 'datasourceId',
      title: '数据源',
      minWidth: 100,
      slots: { default: 'datasource' },
    },
    {
      field: 'status',
      title: '状态',
      width: 80,
      cellRender: {
        name: 'CellDict',
        props: { type: DICT_TYPE.COMMON_STATUS },
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 180,
      formatter: 'formatDateTime',
    },
    {
      title: '操作',
      width: 280,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}

/** 搜索表单字段 */
export function useSearchFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'name',
      label: '报表名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入报表名称',
        allowClear: true,
      },
    },
    {
      fieldName: 'code',
      label: '报表编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入报表编码',
        allowClear: true,
      },
    },
    {
      fieldName: 'datasourceId',
      label: '数据源',
      component: 'Select',
      componentProps: {
        placeholder: '请选择数据源',
        allowClear: true,
        style: {
          width: '200px',
        },
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '报表状态',
        allowClear: true,
        options: [
          { label: '开启', value: 0 },
          { label: '关闭', value: 1 },
        ],
        style: {
          width: '100px',
        },
      },
    },
  ];
}

/** 表单字段 - 基本信息 */
export function useFormBaseSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'reportId',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'name',
      label: '报表名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入报表名称',
      },
      rules: 'required',
    },
    {
      fieldName: 'code',
      label: '报表编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入报表编码',
      },
      rules: 'required',
    },
    {
      fieldName: 'datasourceId',
      label: '数据源',
      component: 'Select',
      componentProps: {
        placeholder: '请选择数据源',
        style: {
          width: '300px',
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'tenant',
      label: '多租户',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      defaultValue: 0,
      rules: 'required',
    },
    {
      fieldName: 'page',
      label: '分页查询',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      defaultValue: 0,
      rules: 'required',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '开启', value: 0 },
          { label: '关闭', value: 1 },
        ],
      },
      defaultValue: 0,
      rules: 'required',
    },
    {
      fieldName: 'sqlContent',
      label: 'SQL内容',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'remark',
      label: '备注',
      component: 'Input',
      componentProps: {
        placeholder: '请输入备注',
      },
    },
  ];
}

/** 参数类型选项 */
export const paramTypeOptions = [
  { label: 'int', value: 'int' },
  { label: 'long', value: 'long' },
  { label: 'string', value: 'string' },
  { label: 'date', value: 'date' },
  { label: 'datetime', value: 'datetime' },
  { label: 'dict', value: 'dict' },
]; 