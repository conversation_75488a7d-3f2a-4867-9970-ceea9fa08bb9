<template>
  <Page auto-content-height>
    <!-- 表单弹窗 -->
    <FormModal @success="gridApi.query" />
    
    <!-- 菜单配置弹窗 -->
    <a-modal v-model:open="configDialogVisible" title="菜单配置" width="500px" @ok="configDialogVisible = false">
      <div class="menu-info">
        <div class="menu-info-item">
          <span class="label">组件地址:</span>
          <span class="value">/common/onlineReport/preview.vue</span>
        </div>
        <div class="menu-info-item">
          <span class="label">组件名称:</span>
          <span class="value">{{ currentReport?.reportId }}</span>
        </div>
      </div>
    </a-modal>
    
    <Grid table-title="在线报表列表">
      <!-- 搜索区域 -->
      <template #toolbar-search>
        <SearchForm @submit="handleQuery" @reset="handleReset">
          <template #datasourceId-comp="{ model }">
            <a-select v-model:value="model.datasourceId" placeholder="请选择数据源" allow-clear style="width: 200px">
              <a-select-option v-for="item in dataSourceOptions" :key="item.id" :value="item.id">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </template>
        </SearchForm>
      </template>
      
      <!-- 工具栏按钮 -->
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['报表']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['infrastructure:online-report:create'],
              onClick: handleCreate,
            },
          ]"
        />
      </template>
      
      <!-- 数据源列 -->
      <template #datasource="{ row }">
        {{ getDataSourceName(row.datasourceId) }}
      </template>
      
      <!-- 操作列 -->
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: '预览',
              type: 'link',
              auth: ['infrastructure:online-report:page'],
              onClick: handlePreview.bind(null, row),
            },
            {
              label: '菜单配置',
              type: 'link',
              onClick: handleConfig.bind(null, row),
            },
            {
              label: $t('common.edit'),
              type: 'link',
              icon: ACTION_ICON.EDIT,
              auth: ['infrastructure:online-report:update'],
              onClick: handleEdit.bind(null, row),
            },
            {
              label: $t('common.delete'),
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              auth: ['infrastructure:online-report:delete'],
              popConfirm: {
                title: $t('ui.actionMessage.deleteConfirm', [row.name]),
                confirm: handleDelete.bind(null, row),
              },
            },
          ]"
        />
      </template>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { InfraDataSourceConfigApi } from '#/api/infra/data-source-config';
import type { OnlineReportVO } from '#/api/infra/online-report';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import { useVbenForm } from '#/adapter/form';
import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import { deleteOnlineReport, getOnlineReport, getOnlineReportPage } from '#/api/infra/online-report';
import { $t } from '#/locales';
import { DICT_TYPE } from '#/utils';

import { useGridColumns, useSearchFormSchema } from './data';
// @ts-ignore
import Form from './modules/form.vue';

const router = useRouter();
const dataSourceOptions = ref<InfraDataSourceConfigApi.DataSourceConfig[]>([]);
const configDialogVisible = ref(false);
const currentReport = ref<OnlineReportVO>();

// 表单
const [SearchForm, searchFormApi] = useVbenForm({
  schema: useSearchFormSchema(),
});

// 表单弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    rowConfig: {
      keyField: 'reportId',
    },
    proxyConfig: {
      ajax: {
        query: async ({ page, sorts, filters, form }: any) => {
          const params = {
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...form,
          };
          return await getOnlineReportPage(params);
        },
      },
    },
  },
});

/** 获取数据源列表 */
const getDataSourceList = async () => {
  try {
    const res = await getDataSourceConfigList();
    dataSourceOptions.value = res || [];
  } catch (error) {
    console.error('获取数据源列表失败', error);
  }
};

/** 获取数据源名称 */
const getDataSourceName = (datasourceId: string) => {
  const dataSource = dataSourceOptions.value.find(item => item.id === Number(datasourceId));
  return dataSource ? dataSource.name : `数据源(${datasourceId})`;
};

/** 搜索按钮操作 */
const handleQuery = async () => {
  await gridApi.query();
};

/** 重置按钮操作 */
const handleReset = async () => {
  if (searchFormApi.resetForm) {
    await searchFormApi.resetForm();
  }
  await gridApi.query();
};

/** 创建报表 */
const handleCreate = () => {
  formModalApi.setData(null).open();
};

/** 编辑报表 */
const handleEdit = (row: OnlineReportVO) => {
  formModalApi.setData(row).open();
};

/** 打开菜单配置弹窗 */
const handleConfig = (row: OnlineReportVO) => {
  currentReport.value = row;
  configDialogVisible.value = true;
};

/** 预览报表 */
const handlePreview = async (row: OnlineReportVO) => {
  try {
    // 先获取报表详情，确保有参数和字段信息
    await getOnlineReport(row.reportId);

    // 获取成功后跳转到预览页面
    router.push({
      path: `/common/online-report/preview/${row.reportId}`
    });
  } catch (error) {
    console.error('获取报表详情失败', error);
    message.error('获取报表详情失败');
  }
};

/** 删除报表 */
const handleDelete = async (row: OnlineReportVO) => {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.name]),
    duration: 0,
    key: 'action_key_msg',
  });
  try {
    await deleteOnlineReport(row.reportId);
    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.name]),
      key: 'action_key_msg',
    });
    await gridApi.query();
  } finally {
    hideLoading();
  }
};

/** 初始化 */
onMounted(async () => {
  await getDataSourceList();
  await gridApi.query();
});
</script>

<style lang="less" scoped>
.menu-info {
  padding: 10px;
  
  .menu-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .label {
      width: 80px;
      font-weight: bold;
      color: #606266;
    }
    
    .value {
      flex: 1;
      margin: 0 10px;
      color: #1890ff;
      word-break: break-all;
    }
  }
}
</style>
