<script lang="ts" setup>
import type { InfraDataSourceConfigApi } from '#/api/infra/data-source-config';
import type {
  OnlineReportColumnDTO,
  OnlineReportColumnVO,
  OnlineReportParamDTO,
  OnlineReportParamVO,
  OnlineReportSaveDTO,
  OnlineReportVO,
} from '#/api/infra/online-report';
import type { SystemDictTypeApi } from '#/api/system/dict/type';

import { computed, nextTick, onErrorCaptured, reactive, ref, watch, onBeforeUnmount } from 'vue';
import { Codemirror } from 'vue-codemirror';

import { useVbenModal } from '@vben/common-ui';

import { useSortable } from '@vueuse/integrations/useSortable';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import {
  createOnlineReport,
  getOnlineReport,
  parseSql,
  updateOnlineReport,
} from '#/api/infra/online-report';
import { getSimpleDictTypeList } from '#/api/system/dict/type';
import { $t } from '#/locales';

import { paramTypeOptions, useFormBaseSchema } from '../data';

const emit = defineEmits(['success']);

// 表单数据
const formData = ref<OnlineReportSaveDTO>({
  name: '',
  code: '',
  sqlContent: '',
  datasourceId: '',
  status: 0,
});

// 数据源选项
const dataSourceOptions = ref<InfraDataSourceConfigApi.DataSourceConfig[]>([]);

// 字典类型选项
const dictTypeOptions = ref<SystemDictTypeApi.DictType[]>([]);

// 全屏状态
const isFullscreen = ref(false);

// 是否使用CodeMirror（如果出错则回退到textarea）
const useCodeMirror = ref(true);

// 标签页
const activeTab = ref('columns');

// 表单标题
const getTitle = computed(() => {
  return formData.value?.reportId
    ? $t('ui.actionTitle.edit', ['报表'])
    : $t('ui.actionTitle.create', ['报表']);
});

// 基本信息表单
const [BaseForm, baseFormApi] = useVbenForm({
  schema: useFormBaseSchema(),
  wrapperClass: 'grid grid-cols-1 md:grid-cols-3 gap-4',
  commonConfig: {
    labelWidth: 80,
  },
  showDefaultActions: false,
});

// 字段列表
const columns = reactive<OnlineReportColumnDTO[]>([]);

// 参数列表
const params = reactive<OnlineReportParamDTO[]>([]);

// 拖拽实例
const columnsSortableInstance = ref<any>(null);
const paramsSortableInstance = ref<any>(null);

// 表格引用
const columnsTableRef = ref();
const paramsTableRef = ref();

// 弹窗
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await baseFormApi.validate();
    if (!valid) {
      return;
    }

    modalApi.lock();

    try {
      // 获取表单值
      const baseValues = await baseFormApi.getValues();

      // 构建提交数据
      const submitData: OnlineReportSaveDTO = {
        ...formData.value,
        ...baseValues,
        columns,
        params,
      };

      // 提交请求
      await (formData.value?.reportId
        ? updateOnlineReport(submitData)
        : createOnlineReport(submitData));

      // 关闭弹窗并提示
      await modalApi.close();
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
    } finally {
      modalApi.unlock();
    }
  },
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      formData.value = {
        name: '',
        code: '',
        sqlContent: '',
        datasourceId: '',
        status: 0,
      };
      columns.length = 0;
      params.length = 0;
      return;
    }

    await nextTick();

    // 加载数据源和字典类型
    await Promise.all([getDataSourceList(), getDictTypeList()]);

    // 加载数据
    const data = modalApi.getData<OnlineReportVO>();
    if (!data || !data.reportId) {
      // 新增
      await baseFormApi.setValues(formData.value);
      return;
    }

    // 编辑
    modalApi.lock();
    try {
      const reportData = await getOnlineReport(data.reportId);
      formData.value = {
        ...reportData,
        params: reportData.params.map((p) => ({
          ...p,
          paramType: p.paramType || 'string',
        })),
      };

      // 设置表单值
      await baseFormApi.setValues(formData.value);

      // SQL编辑器内容会通过v-model自动更新

      // 设置字段和参数
      if (formData.value.columns) {
        columns.push(...formData.value.columns.map((item, index) => ({
          ...item,
          sort: item.sort || (index + 1),
        })));
      }

      if (formData.value.params) {
        params.push(...formData.value.params.map((item, index) => ({
          ...item,
          sort: item.sort || (index + 1),
        })));
      }

      // 初始化拖拽功能
      nextTick(() => {
        if (columns.length > 0) {
          initColumnsSortable();
        }
        if (params.length > 0) {
          initParamsSortable();
        }
      });
    } finally {
      modalApi.unlock();
    }
  },
});

/** 获取数据源列表 */
const getDataSourceList = async () => {
  try {
    const res = await getDataSourceConfigList();
    dataSourceOptions.value = res || [];
    baseFormApi.updateSchema([
      {
        fieldName: 'datasourceId',
        componentProps: {
          options: res.map((item) => ({ label: item.name, value: item.id })),
        },
      },
    ]);
  } catch (error) {
    console.error('获取数据源列表失败', error);
  }
};

/** 获取字典类型列表 */
const getDictTypeList = async () => {
  try {
    const res = await getSimpleDictTypeList();
    dictTypeOptions.value = res || [];
  } catch (error) {
    console.error('获取字典类型列表失败', error);
  }
};

/** 解析SQL */
const handleParseSql = async () => {
  const values = await baseFormApi.getValues();

  if (!values.datasourceId && !values.sqlContent) {
    message.warning('请先选择数据源并输入SQL语句');
    return;
  }

  modalApi.lock();
  try {
    const res = await parseSql({
      dataSourceId: values.datasourceId,
      sql: values.sqlContent,
    });

    // 处理字段
    if (res.columns && res.columns.length > 0) {
      if (columns.length > 0) {
        AntdModal.confirm({
          title: '提示',
          content: '解析出新的字段配置，是否覆盖当前配置？',
          okText: '覆盖',
          cancelText: '取消',
          onOk: () => {
            columns.length = 0;
            columns.push(
              ...res.columns.map((item: OnlineReportColumnVO, index: number) => ({
                ...item,
                paramType: item.paramType || 'string',
                sort: item.sort || (index + 1),
              })),
            );
            // 重新初始化字段拖拽
            nextTick(() => {
              initColumnsSortable();
            });
          },
        });
      } else {
        columns.push(
          ...res.columns.map((item: OnlineReportColumnVO, index: number) => ({
            ...item,
            paramType: item.paramType || 'string',
            sort: item.sort || (index + 1),
          })),
        );
      }
    } else {
      // 如果 res.params 为空，清空当前参数配置
      columns.length = 0;
    }


    // 处理参数
    if (res.params && res.params.length > 0) {
      if (params.length > 0) {
        AntdModal.confirm({
          title: '提示',
          content: '解析出新的参数配置，是否覆盖当前配置？',
          okText: '覆盖',
          cancelText: '取消',
          onOk: () => {
            params.length = 0;
            params.push(
              ...res.params.map((item: OnlineReportParamVO, index: number) => ({
                ...item,
                paramType: item.paramType || 'string',
                sort: item.sort || (index + 1),
              })),
            );
            // 重新初始化参数拖拽
            nextTick(() => {
              initParamsSortable();
            });
          },
        });
      } else {
        params.push(
          ...res.params.map((item: OnlineReportParamVO, index: number) => ({
            ...item,
            paramType: item.paramType || 'string',
            sort: item.sort || (index + 1),
          })),
        );
      }
    } else {
      // 如果 res.params 为空，清空当前参数配置
      params.length = 0;
    }

    // 解析成功后切换到相应的Tab页
    if (res.columns && res.columns.length > 0) {
      activeTab.value = 'columns';
      message.success('SQL解析成功，已生成字段配置');
      // 重新初始化字段拖拽
      nextTick(() => {
        initColumnsSortable();
      });
    } else if (res.params && res.params.length > 0) {
      activeTab.value = 'params';
      message.success('SQL解析成功，已生成参数配置');
      // 重新初始化参数拖拽
      nextTick(() => {
        initParamsSortable();
      });
    } else {
      message.warning('SQL解析成功，但未发现字段或参数');
    }
  } catch (error) {
    console.error('解析SQL失败', error);
    message.error('解析SQL失败，请检查SQL语法');
  } finally {
    modalApi.unlock();
  }
};

/** 新增参数 */
const addParam = () => {
  const nextSort =
    params.length > 0
      ? Math.max(...params.map((item) => Number(item.sort) || 0)) + 1
      : 1;
  params.push({
    paramName: '',
    paramText: '',
    paramType: 'string',
    dictType: '',
    defaultValue: '',
    sort: nextSort,
  });
  // 重新初始化拖拽
  nextTick(() => {
    initParamsSortable();
  });
};

/** 删除参数 */
const deleteParam = (index: number) => {
  params.splice(index, 1);
  // 删除后重新计算序号
  updateParamsSort();
};

/** 新增字段 */
const addColumn = () => {
  const nextSort =
    columns.length > 0
      ? Math.max(...columns.map((item) => Number(item.sort) || 0)) + 1
      : 1;
  columns.push({
    columnName: '',
    columnText: '',
    columnLength: 50,
    paramType: 'string',
    showFlag: true,
    sort: nextSort,
    dictType: '',
  });
  // 重新初始化拖拽
  nextTick(() => {
    initColumnsSortable();
  });
};

/** 删除字段 */
const deleteColumn = (index: number) => {
  columns.splice(index, 1);
  // 删除后重新计算序号
  updateColumnsSort();
};

/** 初始化字段拖拽 */
const initColumnsSortable = () => {
  nextTick(() => {
    const tableElement = columnsTableRef.value?.$el?.querySelector('.ant-table-tbody');
    if (tableElement && columns.length > 0) {
      // 停止现有实例
      if (columnsSortableInstance.value) {
        columnsSortableInstance.value.stop();
        columnsSortableInstance.value = null;
      }

      // 使用 @vueuse/integrations/useSortable
      const sortableRef = useSortable(tableElement, columns, {
        animation: 300,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onUpdate: (evt) => {
          console.log('拖拽更新:', evt);
          // useSortable 会自动处理数组重排，我们只需要更新序号
          nextTick(() => {
            updateColumnsSort();
          });
        }
      });

      // 保存实例引用
      columnsSortableInstance.value = sortableRef;
    }
  });
};

/** 初始化参数拖拽 */
const initParamsSortable = () => {
  nextTick(() => {
    const tableElement = paramsTableRef.value?.$el?.querySelector('.ant-table-tbody');
    console.log('初始化参数拖拽:', { tableElement, paramsLength: params.length, paramsTableRef: paramsTableRef.value });

    if (tableElement && params.length > 0) {
      // 停止现有实例
      if (paramsSortableInstance.value) {
        paramsSortableInstance.value.stop();
        paramsSortableInstance.value = null;
      }

      // 使用 @vueuse/integrations/useSortable
      const sortableRef = useSortable(tableElement, params, {
        animation: 300,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onUpdate: (evt) => {
          console.log('参数拖拽更新:', evt);
          // useSortable 会自动处理数组重排，我们只需要更新序号
          nextTick(() => {
            updateParamsSort();
          });
        }
      });

      // 保存实例引用
      paramsSortableInstance.value = sortableRef;
      console.log('参数拖拽初始化成功:', sortableRef);
    } else {
      console.log('参数拖拽初始化失败:', { tableElement: !!tableElement, paramsLength: params.length });
    }
  });
};

/** 更新字段序号 */
const updateColumnsSort = () => {
  columns.forEach((column, index) => {
    column.sort = index + 1;
  });
};

/** 更新参数序号 */
const updateParamsSort = () => {
  params.forEach((param, index) => {
    param.sort = index + 1;
  });
};

/** 监听SQL内容变化，同步到表单 */
watch(
  () => formData.value.sqlContent,
  (newValue) => {
    baseFormApi.setFieldValue('sqlContent', newValue);
  },
);

/** 监听字段数据变化，初始化拖拽 */
watch(
  () => columns.length,
  (newLength, oldLength) => {
    // 只在数据长度变化时重新初始化
    if (newLength !== oldLength) {
      nextTick(() => {
        initColumnsSortable();
      });
    }
  },
  { flush: 'post' }
);

/** 监听参数数据变化，初始化拖拽 */
watch(
  () => params.length,
  (newLength, oldLength) => {
    // 只在数据长度变化时重新初始化
    if (newLength !== oldLength) {
      nextTick(() => {
        initParamsSortable();
      });
    }
  },
  { flush: 'post' }
);

/** 监听标签页切换，确保拖拽功能正常 */
watch(
  () => activeTab.value,
  (newTab) => {
    nextTick(() => {
      if (newTab === 'params' && params.length > 0) {
        console.log('切换到参数配置标签页，重新初始化拖拽');
        initParamsSortable();
      } else if (newTab === 'columns' && columns.length > 0) {
        console.log('切换到字段配置标签页，重新初始化拖拽');
        initColumnsSortable();
      }
    });
  }
);

/** 捕获组件错误 */
onErrorCaptured((error: any) => {
  if (error.message && error.message.includes('extension')) {
    console.error('CodeMirror extension error:', error);
    useCodeMirror.value = false;
    // message.warning('代码编辑器扩展加载失败，已切换到普通文本编辑器');
    return false; // 阻止错误继续传播
  }
  return true;
});

/** 组件销毁时清理拖拽实例 */
onBeforeUnmount(() => {
  if (columnsSortableInstance.value) {
    columnsSortableInstance.value.stop();
    columnsSortableInstance.value = null;
  }
  if (paramsSortableInstance.value) {
    paramsSortableInstance.value.stop();
    paramsSortableInstance.value = null;
  }
});
</script>

<template>
  <Modal class="h-[90vh] w-[80%]" :title="getTitle" :fullscreen="true">
    <div class="flex h-full flex-col">
      <!-- 基本信息 -->
      <a-card class="mb-4" title="基本信息">
        <BaseForm />
      </a-card>

      <!-- SQL编辑器 -->
      <a-card class="mb-4" title="报表SQL">
        <div class="flex items-start gap-4">
          <div class="flex-1">
            <div class="overflow-hidden rounded border border-gray-300">
              <!-- CodeMirror编辑器 -->
              <Codemirror
                v-if="useCodeMirror"
                v-model="formData.sqlContent"
                :style="{
                  width: '100%',
                  height: isFullscreen ? 'calc(100vh - 100px)' : '300px',
                }"
                placeholder="请输入报表SQL"
                :indent-with-tab="true"
                :tab-size="2"
              />

              <!-- 后备的textarea编辑器 -->
              <a-textarea
                v-else
                v-model:value="formData.sqlContent"
                :style="{
                  width: '100%',
                  height: isFullscreen ? 'calc(100vh - 100px)' : '300px',
                  resize: 'none',
                  fontFamily: 'Monaco, Menlo, Consolas, monospace',
                }"
                placeholder="请输入报表SQL"
                class="sql-editor"
              />
            </div>
          </div>
          <div class="flex flex-col gap-2">
            <a-button
              type="primary"
              @click="handleParseSql"
              :disabled="!formData.datasourceId && !formData.sqlContent"
            >
              解析SQL
            </a-button>
            <a-button @click="isFullscreen = !isFullscreen">
              {{ isFullscreen ? '退出全屏' : '全屏编辑' }}
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 字段和参数配置 -->
      <a-card class="mb-4" title="字段和参数配置">
        <a-tabs v-model:active-key="activeTab" class="h-full">
          <!-- 字段配置 -->
          <a-tab-pane key="columns" tab="字段配置">
            <div class="p-4">
              <div class="mb-4 flex items-center gap-2">
                <a-button type="primary" @click="addColumn"> 新增字段 </a-button>
                <span v-if="columns.length > 1" class="text-gray-500">
                  <span class="icon-[ic--round-drag-indicator] mr-1"></span>
                  拖拽表格行可调整顺序
                </span>
              </div>

              <div class="mt-4">
                <a-table
                  ref="columnsTableRef"
                  :data-source="columns"
                  :pagination="false"
                  size="small"
                  bordered
                  row-key="columnName"
                  :scroll="{ y: 500 }"
                  :custom-row="() => ({ class: 'cursor-move' })"
                >
                  <a-table-column title="序号" align="center" width="80">
                    <template #default="{ record, index }">
                      <div class="flex items-center justify-center gap-2">
                        <span
                          class="icon-[ic--round-drag-indicator] cursor-move text-gray-400"
                          title="拖拽排序"
                        ></span>
                        <span>{{ record?.sort || (index + 1) }}</span>
                      </div>
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="字段名称"
                    data-index="columnName"
                    width="150"
                  >
                    <template #default="{ record }">
                      <a-input
                        v-model:value="record.columnName"
                        placeholder="请输入字段名称"
                        :disabled="true"
                      />
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="字段文本"
                    data-index="columnText"
                    width="150"
                  >
                    <template #default="{ record }">
                      <a-input
                        v-model:value="record.columnText"
                        placeholder="请输入字段文本"
                      />
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="字段类型"
                    data-index="paramType"
                    width="120"
                  >
                    <template #default="{ record }">
                      <a-select
                        v-model:value="record.paramType"
                        placeholder="请选择字段类型"
                        style="width: 100%"
                      >
                        <a-select-option
                          v-for="item in paramTypeOptions"
                          :key="item.value"
                          :value="item.value"
                        >
                          {{ item.label }}
                        </a-select-option>
                      </a-select>
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="字段长度"
                    data-index="columnLength"
                    width="100"
                  >
                    <template #default="{ record }">
                      <a-input-number
                        v-model:value="record.columnLength"
                        :min="0"
                        style="width: 100%"
                      />
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="是否显示"
                    data-index="showFlag"
                    width="80"
                  >
                    <template #default="{ record }">
                      <a-checkbox v-model:checked="record.showFlag">
                        显示
                      </a-checkbox>
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="字典类型"
                    data-index="dictType"
                    width="150"
                  >
                    <template #default="{ record }">
                      <a-select
                        v-model:value="record.dictType"
                        placeholder="请选择字典类型"
                        allow-clear
                        style="width: 100%"
                        :disabled="record.paramType !== 'dict'"
                      >
                        <a-select-option
                          v-for="dict in dictTypeOptions"
                          :key="dict.type"
                          :value="dict.type"
                        >
                          {{ dict.name }}
                        </a-select-option>
                      </a-select>
                    </template>
                  </a-table-column>

                  <a-table-column title="操作" width="80">
                    <template #default="{ index }">
                      <a-button type="link" danger @click="deleteColumn(index)">
                        删除
                      </a-button>
                    </template>
                  </a-table-column>
                </a-table>
              </div>
            </div>
          </a-tab-pane>

          <!-- 参数配置 -->
          <a-tab-pane key="params" tab="参数配置">
            <div class="p-4">
              <div class="mb-4 flex items-center gap-2">
                <a-button type="primary" @click="addParam"> 新增参数 </a-button>
                <span v-if="params.length > 1" class="text-gray-500">
                  <span class="icon-[ic--round-drag-indicator] mr-1"></span>
                  拖拽表格行可调整顺序
                </span>
              </div>

              <div class="mt-4">
                <a-table
                  ref="paramsTableRef"
                  :data-source="params"
                  :pagination="false"
                  size="small"
                  bordered
                  row-key="paramName"
                  :scroll="{ y: 500 }"
                  :custom-row="() => ({ class: 'cursor-move' })"
                >
                  <a-table-column title="序号" align="center" width="80">
                    <template #default="{ record, index }">
                      <div class="flex items-center justify-center gap-2">
                        <span
                          class="icon-[ic--round-drag-indicator] cursor-move text-gray-400"
                          title="拖拽排序"
                        ></span>
                        <span>{{ record?.sort || (index + 1) }}</span>
                      </div>
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="参数名称"
                    data-index="paramName"
                    width="150"
                  >
                    <template #default="{ record }">
                      <a-input
                        v-model:value="record.paramName"
                        placeholder="请输入参数名称"
                        :disabled="true"
                      />
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="参数文本"
                    data-index="paramText"
                    width="150"
                  >
                    <template #default="{ record }">
                      <a-input
                        v-model:value="record.paramText"
                        placeholder="请输入参数文本"
                      />
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="参数类型"
                    data-index="paramType"
                    width="120"
                  >
                    <template #default="{ record }">
                      <a-select
                        v-model:value="record.paramType"
                        placeholder="请选择参数类型"
                        style="width: 100%"
                      >
                        <a-select-option
                          v-for="item in paramTypeOptions"
                          :key="item.value"
                          :value="item.value"
                        >
                          {{ item.label }}
                        </a-select-option>
                      </a-select>
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="字典类型"
                    data-index="dictType"
                    width="150"
                  >
                    <template #default="{ record }">
                      <a-select
                        v-model:value="record.dictType"
                        placeholder="请选择字典类型"
                        allow-clear
                        style="width: 100%"
                        :disabled="record.paramType !== 'dict'"
                      >
                        <a-select-option
                          v-for="dict in dictTypeOptions"
                          :key="dict.type"
                          :value="dict.type"
                        >
                          {{ dict.name }}
                        </a-select-option>
                      </a-select>
                    </template>
                  </a-table-column>

                  <a-table-column
                    title="默认值"
                    data-index="defaultValue"
                    width="150"
                  >
                    <template #default="{ record }">
                      <a-input
                        v-model:value="record.defaultValue"
                        placeholder="请输入默认值"
                      />
                    </template>
                  </a-table-column>
                </a-table>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.sql-editor-container {
  position: relative;
  width: 100%;
  transition: all 0.3s;
}

.sql-editor {
  width: 100%;
  min-height: 200px;
  transition: all 0.3s;
}

.sql-button-container {
  position: absolute;
  right: 50px;
  top: 10px;
  z-index: 20;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.fullscreen-button-container {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 20;
  transition: all 0.3s;
}

/* 全屏模式样式 */
.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2000;
  background-color: #fff;
  padding: 20px;
  box-sizing: border-box;
}

.fullscreen-mode .sql-editor {
  height: calc(100vh - 100px) !important;
}

/* 拖拽样式 */
.sortable-ghost {
  opacity: 0.5;
  background: #f0f0f0;
}

.sortable-chosen {
  background: #e6f7ff;
}

.sortable-drag {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 拖拽时的表格行样式 */
:deep(.ant-table-tbody > tr.cursor-move:hover) {
  background-color: #f5f5f5;
}
</style>
