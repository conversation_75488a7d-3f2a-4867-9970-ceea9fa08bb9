<template>
  <Page>
    <Grid @register="registerGrid">
      <template #datasource="{ record }">
        {{ getDataSourceName(record.datasourceId) }}
      </template>
      
      <template #actions="{ record }">
        <TableAction
          :actions="[
            {
              label: '同步DB',
              onClick: () => handleSync(record),
              auth: 'infra:online-table:sync',
              disabled: record.status === 1,
            },
            {
              label: '预览',
              onClick: () => handlePreview(record),
              auth: 'infra:online-table:query',
            },
            {
              label: '历史',
              onClick: () => handleHistory(record),
              auth: 'infra:online-table:query',
            },
            {
              label: '复制',
              onClick: () => handleCopy(record),
              auth: 'infra:online-table:create',
            },
            {
              label: '编辑',
              onClick: () => openForm('update', record),
              auth: 'infra:online-table:update',
            },
            {
              label: '删除',
              color: 'error',
              onClick: () => handleDelete(record),
              auth: 'infra:online-table:delete',
            },
          ]"
        />
      </template>
    </Grid>
    
    <!-- 表单弹窗：添加/修改 -->
    <TableForm @register="registerModal" @success="gridApi.reload" />
    
    <!-- 历史版本弹窗 -->
    <HistoryDialog @register="registerHistoryModal" />
  </Page>
</template>

<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { message, Modal } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import {
  copyOnlineTable,
  deleteOnlineTable,
  getOnlineTablePage,
  syncOnlineTable,
} from '#/api/infra/onlineTable';
import { $t } from '#/locales';

import { useGridColumns, useSearchFormSchema } from './data';
import TableForm from './modules/form.vue';
import HistoryDialog from './modules/history.vue';

defineOptions({ name: 'OnlineTableIndex' });

const router = useRouter();

// 数据源选项
const dataSourceOptions = ref<any[]>([]);

// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useSearchFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: 'auto',
    rowConfig: {
      keyField: 'tableId',
    },
    proxyConfig: {
      ajax: {
        query: async ({ page, sorts, filters, form }: any) => {
          const params = {
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...form,
          };
          return await getOnlineTablePage(params);
        },
      },
    },
  },
  toolbarConfig: {
    buttons: [
      {
        content: $t('ui.actionTitle.create', ['表单']),
        buttonProps: {
          type: 'primary',
        },
        icon: ACTION_ICON.ADD,
        onClick: () => openForm('create'),
      },
    ],
  },
});

// 表单弹窗
const [Modal, modalApi] = useVbenModal({
  connectedComponent: TableForm,
});

// 历史版本弹窗
const [HistoryModal, historyModalApi] = useVbenModal({
  connectedComponent: HistoryDialog,
});

/** 获取数据源列表 */
const getDataSourceList = async () => {
  try {
    const res = await getDataSourceConfigList();
    dataSourceOptions.value = res || [];
    
    // 更新搜索表单的数据源选项
    const searchFormSchema = useSearchFormSchema();
    const datasourceField = searchFormSchema.find(item => item.fieldName === 'datasourceId');
    if (datasourceField && datasourceField.componentProps) {
      datasourceField.componentProps.options = dataSourceOptions.value.map(item => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取数据源列表失败', error);
  }
};

/** 添加/修改操作 */
const openForm = (type: 'create' | 'update', record?: OnlineTableVO) => {
  modalApi.open({
    title: type === 'create' ? $t('ui.actionTitle.create', ['表单']) : $t('ui.actionTitle.edit', ['表单']),
    data: { type, record },
  });
};

/** 删除按钮操作 */
const handleDelete = async (record: OnlineTableVO) => {
  Modal.confirm({
    title: '系统提示',
    content: `是否确认删除表格编号为"${record.tableId}"的数据项?`,
    onOk: async () => {
      try {
        await deleteOnlineTable(record.tableId);
        message.success('删除成功');
        gridApi.reload();
      } catch (error) {
        console.error('删除失败', error);
      }
    },
  });
};

/** 复制按钮操作 */
const handleCopy = async (record: OnlineTableVO) => {
  Modal.confirm({
    title: '警告',
    content: `是否确认复制表格"${record.tableName}"?`,
    onOk: async () => {
      try {
        await copyOnlineTable(record.tableId);
        message.success('复制成功');
        gridApi.reload();
      } catch (error) {
        console.error('复制失败', error);
      }
    },
  });
};

/** 同步数据库 */
const handleSync = async (record: OnlineTableVO) => {
  Modal.confirm({
    title: '警告',
    content: `是否确认同步表格"${record.tableName}"到数据库?`,
    onOk: async () => {
      try {
        await syncOnlineTable(record.tableId);
        message.success('同步成功');
        gridApi.reload();
      } catch (error) {
        console.error('同步失败', error);
      }
    },
  });
};

/** 预览表单 */
const handlePreview = (record: OnlineTableVO) => {
  router.push({
    path: `/common/online-table/preview/${record.tableId}`,
  });
};

/** 历史版本 */
const handleHistory = (record: OnlineTableVO) => {
  historyModalApi.open({
    title: '历史版本',
    data: { tableName: record.tableName },
  });
};

/** 获取数据源名称 */
const getDataSourceName = (datasourceId: string) => {
  const dataSource = dataSourceOptions.value.find(item => item.id === datasourceId);
  return dataSource ? dataSource.name : '未知数据源';
};

// 注册组件
const registerGrid = gridApi.register;
const registerModal = modalApi.register;
const registerHistoryModal = historyModalApi.register;

/** 初始化 */
onMounted(() => {
  getDataSourceList();
});
</script>
