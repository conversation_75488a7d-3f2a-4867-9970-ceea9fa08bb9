<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { message, Modal } from 'ant-design-vue';

import { ACTION_ICON, TableAction, useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import {
  copyOnlineTable,
  deleteOnlineTable,
  getOnlineTablePage,
  syncOnlineTable,
} from '#/api/infra/onlineTable';
import { $t } from '#/locales';

import { useGridColumns, useSearchFormSchema } from './data';
import TableForm from './modules/form.vue';
import HistoryDialog from './modules/history.vue';

defineOptions({ name: 'OnlineTableIndex' });

const router = useRouter();

// 数据源选项
const dataSourceOptions = ref<any[]>([]);

// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useSearchFormSchema(),
  },
  gridOptions: {
    columns: useGridColumns(),
    height: auto,
    keepSource: true,
    rowConfig: {
      keyField: 'tableId',
      isHover: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const params = {
            pageNo: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };
          return await getOnlineTablePage(params);
        },
      },
    },
    toolbarConfig: {
      refresh: { code: 'query' },
      search: true,
    },
  } as VxeTableGridOptions<OnlineTableVO>,
});

// 表单弹窗
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: TableForm,
  destroyOnClose: true,
});

// 历史版本弹窗
const [HistoryModal, historyModalApi] = useVbenModal({
  connectedComponent: HistoryDialog,
  destroyOnClose: true,
});

/** 获取数据源列表 */
const getDataSourceList = async () => {
  try {
    const res = await getDataSourceConfigList();
    dataSourceOptions.value = res || [];
  } catch (error) {
    console.error('获取数据源列表失败', error);
  }
};

/** 创建表单 */
const handleCreate = () => {
  formModalApi.setData({ type: 'create' }).open();
};

/** 编辑表单 */
const handleEdit = (row: OnlineTableVO) => {
  formModalApi.setData({ type: 'update', record: row }).open();
};

/** 删除按钮操作 */
const handleDelete = async (record: OnlineTableVO) => {
  try {
    await deleteOnlineTable(record.tableId);
    message.success('删除成功');
    gridApi.query();
  } catch (error) {
    console.error('删除失败', error);
  }
};

/** 复制按钮操作 */
const handleCopy = async (record: OnlineTableVO) => {
  Modal.confirm({
    title: '警告',
    content: `是否确认复制表格"${record.tableName}"?`,
    onOk: async () => {
      try {
        await copyOnlineTable(record.tableId);
        message.success('复制成功');
        gridApi.query();
      } catch (error) {
        console.error('复制失败', error);
      }
    },
  });
};

/** 同步数据库 */
const handleSync = async (record: OnlineTableVO) => {
  Modal.confirm({
    title: '警告',
    content: `是否确认同步表格"${record.tableName}"到数据库?`,
    onOk: async () => {
      try {
        await syncOnlineTable(record.tableId);
        message.success('同步成功');
        gridApi.query();
      } catch (error) {
        console.error('同步失败', error);
      }
    },
  });
};

/** 预览表单 */
const handlePreview = (record: OnlineTableVO) => {
  router.push({
    path: `/common/online-table/preview/${record.tableId}`,
  });
};

/** 历史版本 */
const handleHistory = (record: OnlineTableVO) => {
  historyModalApi.setData({ tableName: record.tableName }).open();
};

/** 获取数据源名称 */
const getDataSourceName = (datasourceId: string) => {
  const dataSource = dataSourceOptions.value.find(
    (item) => item.id === datasourceId,
  );
  return dataSource ? dataSource.name : '未知数据源';
};

/** 初始化 */
onMounted(() => {
  getDataSourceList();
});
</script>

<template>
  <Page auto-content-height>
    <Grid table-title="在线表单列表">
      <!-- 数据源选择组件 -->
      <template #datasourceId-comp="{ model }">
        <a-select
          v-model:value="model.datasourceId"
          placeholder="请选择数据源"
          allow-clear
          style="width: 200px"
        >
          <a-select-option
            v-for="item in dataSourceOptions"
            :key="item.id"
            :value="item.id"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </template>

      <!-- 工具栏按钮 -->
      <template #toolbar-tools>
        <TableAction
          :actions="[
            {
              label: $t('ui.actionTitle.create', ['表单']),
              type: 'primary',
              icon: ACTION_ICON.ADD,
              auth: ['infra:online-table:create'],
              onClick: handleCreate,
            },
          ]"
        />
      </template>

      <!-- 数据源列 -->
      <template #datasource="{ row }">
        {{ getDataSourceName(row?.datasourceId) }}
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <TableAction
          :actions="[
            {
              label: '同步DB',
              type: 'link',
              onClick: () => handleSync(row),
              auth: ['infra:online-table:sync'],
              disabled: row.status === 1,
            },
            {
              label: '预览',
              type: 'link',
              onClick: () => handlePreview(row),
              auth: ['infra:online-table:query'],
            },
            {
              label: '历史',
              type: 'link',
              onClick: () => handleHistory(row),
              auth: ['infra:online-table:query'],
            },
            {
              label: '复制',
              type: 'link',
              onClick: () => handleCopy(row),
              auth: ['infra:online-table:create'],
            },
            {
              label: '编辑',
              type: 'link',
              icon: ACTION_ICON.EDIT,
              onClick: () => handleEdit(row),
              auth: ['infra:online-table:update'],
            },
            {
              label: '删除',
              type: 'link',
              danger: true,
              icon: ACTION_ICON.DELETE,
              auth: ['infra:online-table:delete'],
              popConfirm: {
                title: '是否确认删除该条数据?',
                confirm: () => handleDelete(row),
              },
            },
          ]"
        />
      </template>
    </Grid>

    <!-- 表单弹窗：添加/修改 -->
    <FormModal />

    <!-- 历史版本弹窗 -->
    <HistoryModal />
  </Page>
</template>
