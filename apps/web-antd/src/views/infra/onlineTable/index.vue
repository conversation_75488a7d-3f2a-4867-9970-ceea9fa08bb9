<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-card shadow="never" class="search-wrapper">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
        <el-form-item label="表格名称" prop="tableName">
          <el-input
            v-model="queryParams.tableName"
            placeholder="请输入表格名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="表格备注" prop="tableComment">
          <el-input
            v-model="queryParams.tableComment"
            placeholder="请输入表格备注"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="表单类型" prop="tableType">
          <el-select v-model="queryParams.tableType" placeholder="请选择表单类型" clearable style="width: 150px">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ONLINE_TABLE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="datasourceId" style="width: 200px">
          <el-select v-model="queryParams.datasourceId" placeholder="请选择数据源" clearable>
            <el-option
              v-for="item in dataSourceOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="同步状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="同步状态" clearable style="width: 120px">
            <el-option label="未同步" :value="0" />
            <el-option label="已同步" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="多租户" prop="tenant">
          <el-select v-model="queryParams.tenant" placeholder="多租户" clearable style="width: 100px">
            <el-option label="否" :value="0" />
            <el-option label="是" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />
            重置
          </el-button>
          <el-button
            type="primary"
            plain
            @click="openForm('create')"
            v-hasPermi="['infrastructure:online-table:create']"
          >
            <Icon icon="ep:plus" class="mr-5px" />
            新增
          </el-button>
          <el-button
            type="success"
            plain
            @click="handleImport"
            v-hasPermi="['infrastructure:online-table:import']"
          >
            <Icon icon="ep:upload" class="mr-5px" />
            导入
          </el-button>
          <el-button
            type="warning"
            plain
            @click="handleExportConfig"
            v-hasPermi="['infrastructure:online-table:export']"
          >
            <Icon icon="ep:download" class="mr-5px" />
            导出配置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 列表 -->
    <el-card shadow="never" class="mt-10px">
      <el-table v-loading="loading" :data="list">
        <el-table-column label="表格ID" align="center" prop="tableId" width="80" />
        <el-table-column label="表格名称" align="center" prop="tableName" min-width="120" />
        <el-table-column label="表格备注" align="center" prop="tableComment" min-width="120" />
        <el-table-column label="表单类型" align="center" prop="tableType" width="100">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.ONLINE_TABLE_TYPE" :value="scope.row.tableType" />
          </template>
        </el-table-column>
        <el-table-column label="主键类型" align="center" prop="privateKeyType" width="100">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.ONLINE_TABLE_PRIVATE_KEY_TYPE" :value="scope.row.privateKeyType" />
          </template>
        </el-table-column>
        <el-table-column label="数据源" align="center" width="120">
          <template #default="scope">
            {{ getDataSourceName(scope.row.datasourceId) }}
          </template>
        </el-table-column>
        <el-table-column label="版本号" align="center" prop="version" width="80">
          <template #default="scope">
            <el-tag type="info" size="small">v{{ scope.row.version || 1 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="同步状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'warning'">
              {{ scope.row.status === 1 ? '已同步' : '未同步' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="多租户" align="center" prop="tenant" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.tenant === 1 ? 'success' : 'info'">
              {{ scope.row.tenant === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分页" align="center" prop="page" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.page === 1 ? 'success' : 'info'">
              {{ scope.row.page === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :formatter="dateFormatter"
          width="180"
        />
        <el-table-column label="操作" align="center" width="380" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleSync(scope.row)"
              v-hasPermi="['infrastructure:online-table:sync']"
              :disabled="scope.row.status === 1"
            >
              同步DB
            </el-button>
            <el-button
              link
              type="primary"
              @click="handlePreview(scope.row)"
              v-hasPermi="['infrastructure:online-table:preview']"
            >
              预览
            </el-button>
            <el-button
              link
              type="info"
              @click="handleHistory(scope.row)"
              v-hasPermi="['infrastructure:online-table:history']"
            >
              历史
            </el-button>
            <el-button
              link
              type="success"
              @click="handleCopy(scope.row)"
              v-hasPermi="['infrastructure:online-table:copy']"
            >
              复制
            </el-button>
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.tableId)"
              v-hasPermi="['infrastructure:online-table:update']"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['infrastructure:online-table:delete']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 表单弹窗：添加/修改 -->
    <TableForm ref="formRef" @success="getList" />

    <!-- 历史版本弹窗 -->
    <HistoryDialog ref="historyRef" />
  </div>
</template>

<script lang="ts" setup>
import {DICT_TYPE, getIntDictOptions} from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as OnlineTableApi from '@/api/infra/onlineTable'
import * as DataSourceConfigApi from '@/api/infra/dataSourceConfig'
import TableForm from './form.vue'
import HistoryDialog from './history.vue'
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(true)
const total = ref(0)
const list = ref<OnlineTableApi.OnlineTableVO[]>([])
const queryFormRef = ref()
const formRef = ref()
const historyRef = ref()
const dataSourceOptions = ref<DataSourceConfigApi.DataSourceConfigVO[]>([])

const queryParams = reactive<OnlineTableApi.OnlineTableQueryDTO>({
  pageNo: 1,
  pageSize: 10,
  tableName: undefined,
  tableComment: undefined,
  tableType: undefined,
  datasourceId: undefined,
  status: undefined,
  tenant: undefined
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OnlineTableApi.getOnlineTablePage(queryParams)
    list.value = data.list
    total.value = data.total

    // 获取数据源列表
    await getDataSourceList()
  } finally {
    loading.value = false
  }
}

/** 获取数据源列表 */
const getDataSourceList = async () => {
  try {
    const res = await DataSourceConfigApi.getDataSourceConfigList()
    dataSourceOptions.value = res || []
  } catch (error) {
    console.error('获取数据源列表失败', error)
  }
}

/** 获取数据源名称 */
const getDataSourceName = (datasourceId: number) => {
  const dataSource = dataSourceOptions.value.find(item => item.id === datasourceId)
  return dataSource ? dataSource.name : `数据源(${datasourceId})`
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 打开表单 */
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 同步数据库 */
const handleSync = async (row: OnlineTableApi.OnlineTableVO) => {
  try {
    await ElMessageBox.confirm('是否确认同步表格"' + row.tableName + '"到数据库?', '警告', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    await OnlineTableApi.syncOnlineTable(row.tableId)
    ElMessage.success('同步成功')
    await getList()
  } catch {}
}

/** 预览表单 */
const handlePreview = async (row: OnlineTableApi.OnlineTableVO) => {
  try {
    // 先获取表单详情，确保有字段和页面配置信息
    await OnlineTableApi.getOnlineTable(row.tableId)

    // 获取成功后跳转到预览页面
    router.push({
      path: `/common/online-table/preview/${row.tableId}`
    })
  } catch (error) {
    console.error('获取表单详情失败', error)
    ElMessage.error('获取表单详情失败')
  }
}

/** 查看历史版本 */
const handleHistory = (row: OnlineTableApi.OnlineTableVO) => {
  historyRef.value.open(row.tableName, row.tableComment)
}

/** 复制表单 */
const handleCopy = async (row: OnlineTableApi.OnlineTableVO) => {
  try {
    await ElMessageBox.confirm('是否确认复制表格"' + row.tableName + '"?', '提示', {
      type: 'info',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    await OnlineTableApi.copyOnlineTable(row.tableId)
    ElMessage.success('复制成功')
    await getList()
  } catch {}
}

/** 导入配置 */
const handleImport = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = async (e: any) => {
    const file = e.target.files[0]
    if (!file) return

    try {
      await OnlineTableApi.importOnlineTableConfig(file)
      ElMessage.success('导入成功')
      await getList()
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败')
    }
  }
  input.click()
}

/** 导出配置 */
const handleExportConfig = async () => {
  if (list.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  try {
    // 这里可以选择导出所有配置或选中的配置
    await ElMessageBox.confirm('是否确认导出所有表单配置?', '提示', {
      type: 'info',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    // 导出所有配置（这里需要后端支持批量导出）
    // 或者可以循环导出每个表单的配置
    for (const item of list.value) {
      await OnlineTableApi.exportOnlineTableConfig(item.tableId)
    }

    ElMessage.success('导出成功')
  } catch {}
}

/** 删除按钮操作 */
const handleDelete = async (row: OnlineTableApi.OnlineTableVO) => {
  try {
    await ElMessageBox.confirm('是否确认删除表格名称为"' + row.tableName + '"的数据项?', '警告', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    await OnlineTableApi.deleteOnlineTable(row.tableId)
    ElMessage.success('删除成功')
    await getList()
  } catch {}
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.search-wrapper {
  margin-bottom: 10px;
}
</style>
