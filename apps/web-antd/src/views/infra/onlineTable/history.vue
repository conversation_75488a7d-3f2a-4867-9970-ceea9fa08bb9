<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${tableComment} - 历史版本`"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="history-container">
      <!-- 搜索栏 -->
      <el-card shadow="never" class="search-card">
        <el-form :inline="true" class="search-form">
          <el-form-item label="版本号">
            <el-input
              v-model="searchVersion"
              placeholder="请输入版本号"
              clearable
              style="width: 150px"
              @input="handleSearch"
            />
          </el-form-item>
          <el-form-item label="创建者">
            <el-input
              v-model="searchCreator"
              placeholder="请输入创建者"
              clearable
              style="width: 150px"
              @input="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <Icon icon="ep:refresh" class="mr-5px" />
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 历史版本列表 -->
      <el-card shadow="never" class="mt-10px">
        <div v-loading="loading" class="history-list">
          <el-empty v-if="!loading && historyList.length === 0" description="暂无历史版本" />
          
          <div v-else class="version-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in filteredHistoryList"
                :key="item.tableId"
                :timestamp="formatTime(item.createTime)"
                placement="top"
                :type="index === 0 ? 'primary' : 'info'"
                :size="index === 0 ? 'large' : 'normal'"
              >
                <el-card class="version-card" :class="{ 'current-version': index === 0 }">
                  <template #header>
                    <div class="version-header">
                      <div class="version-info">
                        <el-tag 
                          :type="index === 0 ? 'success' : 'info'" 
                          size="large"
                          class="version-tag"
                        >
                          v{{ item.version || 1 }}
                          <span v-if="index === 0" class="current-label">（当前版本）</span>
                        </el-tag>
                        <span class="creator">创建者：{{ item.creator || '系统' }}</span>
                      </div>
                      <div class="version-actions">
                        <el-button
                          type="primary"
                          size="small"
                          @click="handleViewDetail(item)"
                        >
                          查看详情
                        </el-button>
                        <el-button
                          v-if="index !== 0"
                          type="success"
                          size="small"
                          @click="handleCompare(item, historyList[0])"
                        >
                          与当前版本对比
                        </el-button>
                        <el-button
                          v-if="index !== 0"
                          type="warning"
                          size="small"
                          @click="handleRestore(item)"
                        >
                          恢复此版本
                        </el-button>
                      </div>
                    </div>
                  </template>
                  
                  <div class="version-content">
                    <el-descriptions :column="2" size="small">
                      <el-descriptions-item label="表格名称">
                        {{ item.tableName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="表格备注">
                        {{ item.tableComment }}
                      </el-descriptions-item>
                      <el-descriptions-item label="表单类型">
                        <dict-tag :type="DICT_TYPE.ONLINE_TABLE_TYPE" :value="item.tableType" />
                      </el-descriptions-item>
                      <el-descriptions-item label="主键类型">
                        <dict-tag :type="DICT_TYPE.ONLINE_TABLE_PRIVATE_KEY_TYPE" :value="item.privateKeyType" />
                      </el-descriptions-item>
                      <el-descriptions-item label="字段数量">
                        <el-tag type="info" size="small">{{ item.columns?.length || 0 }} 个字段</el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="页面配置">
                        <el-tag type="info" size="small">{{ item.pageConfigs?.length || 0 }} 个配置</el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="同步状态">
                        <el-tag :type="item.status === 1 ? 'success' : 'warning'" size="small">
                          {{ item.status === 1 ? '已同步' : '未同步' }}
                        </el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="多租户">
                        <el-tag :type="item.tenant === 1 ? 'success' : 'info'" size="small">
                          {{ item.tenant === 1 ? '是' : '否' }}
                        </el-tag>
                      </el-descriptions-item>
                    </el-descriptions>
                    
                    <!-- 字段预览 -->
                    <div class="field-preview mt-10px">
                      <el-divider content-position="left">字段预览</el-divider>
                      <div class="field-tags">
                        <el-tag
                          v-for="column in (item.columns || []).slice(0, 8)"
                          :key="column.columnId"
                          size="small"
                          class="field-tag"
                          :type="column.privateKey ? 'danger' : 'info'"
                        >
                          {{ column.columnName }}
                          <span class="field-type">{{ column.columnType }}</span>
                        </el-tag>
                        <el-tag
                          v-if="(item.columns || []).length > 8"
                          size="small"
                          type="info"
                          class="field-tag"
                        >
                          +{{ (item.columns || []).length - 8 }} 个字段
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 详情弹窗 -->
  <DetailDialog ref="detailRef" />
  
  <!-- 对比弹窗 -->
  <CompareDialog ref="compareRef" />
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import * as OnlineTableApi from '@/api/infra/onlineTable/config'
import DetailDialog from './historyDetail.vue'
import CompareDialog from './historyCompare.vue'

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const tableName = ref('')
const tableComment = ref('')
const historyList = ref<OnlineTableApi.OnlineTableVO[]>([])
const searchVersion = ref('')
const searchCreator = ref('')

// 组件引用
const detailRef = ref()
const compareRef = ref()

// 计算属性 - 过滤后的历史列表
const filteredHistoryList = computed(() => {
  let list = historyList.value
  
  if (searchVersion.value) {
    list = list.filter(item => 
      String(item.version || 1).includes(searchVersion.value)
    )
  }
  
  if (searchCreator.value) {
    list = list.filter(item => 
      (item.creator || '').toLowerCase().includes(searchCreator.value.toLowerCase())
    )
  }
  
  return list
})

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return ''
  return formatDate(new Date(timestamp), 'YYYY-MM-DD HH:mm:ss')
}

// 打开弹窗
const open = async (name: string, comment: string) => {
  tableName.value = name
  tableComment.value = comment
  dialogVisible.value = true
  
  await loadHistoryList()
}

// 加载历史版本列表
const loadHistoryList = async () => {
  if (!tableName.value) return
  
  loading.value = true
  try {
    const data = await OnlineTableApi.getHistoryVersions(tableName.value)
    historyList.value = data || []
    
    // 按版本号和创建时间降序排序
    historyList.value.sort((a, b) => {
      const versionA = a.version || 1
      const versionB = b.version || 1
      if (versionA !== versionB) {
        return versionB - versionA
      }
      return (b.createTime || 0) - (a.createTime || 0)
    })
  } catch (error) {
    console.error('获取历史版本失败:', error)
    ElMessage.error('获取历史版本失败')
    historyList.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

// 重置搜索
const resetSearch = () => {
  searchVersion.value = ''
  searchCreator.value = ''
}

// 查看详情
const handleViewDetail = (item: OnlineTableApi.OnlineTableVO) => {
  detailRef.value.open(item)
}

// 版本对比
const handleCompare = (oldVersion: OnlineTableApi.OnlineTableVO, currentVersion: OnlineTableApi.OnlineTableVO) => {
  compareRef.value.open(oldVersion, currentVersion)
}

// 恢复版本
const handleRestore = async (item: OnlineTableApi.OnlineTableVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复到版本 v${item.version || 1} 吗？恢复后当前版本将被覆盖。`,
      '确认恢复',
      {
        type: 'warning',
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消'
      }
    )
    
    // 这里需要调用恢复版本的API（如果后端提供）
    // await OnlineTableApi.restoreVersion(item.tableId)
    
    ElMessage.success('版本恢复成功')
    await loadHistoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复版本失败:', error)
      ElMessage.error('恢复版本失败')
    }
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.history-container {
  .search-card {
    margin-bottom: 16px;
    
    .search-form {
      margin-bottom: 0;
    }
  }
  
  .history-list {
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
  }
  
  .version-timeline {
    padding: 20px;
    
    .version-card {
      margin-bottom: 16px;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.current-version {
        border: 2px solid #67c23a;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);
      }
    }
    
    .version-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .version-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .version-tag {
          font-weight: bold;
          
          .current-label {
            font-size: 12px;
            margin-left: 4px;
          }
        }
        
        .creator {
          color: #666;
          font-size: 14px;
        }
      }
      
      .version-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .version-content {
      .field-preview {
        .field-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .field-tag {
            .field-type {
              margin-left: 4px;
              opacity: 0.7;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .version-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;
    
    .version-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }
  
  .field-tags {
    .field-tag {
      font-size: 12px !important;
    }
  }
}
</style>
