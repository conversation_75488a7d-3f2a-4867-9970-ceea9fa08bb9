<script lang="ts" setup>
// 页面配置实际上是字段的页面配置，不是OnlineTablePageDTO
interface PageConfigItem {
  columnName: string;
  columnText: string;
  queryFlag: boolean;
  formShowFlag: boolean;
  listShowFlag: boolean;
  readonlyFlag: boolean;
  sortFlag: boolean;
  queryType: string;
  componentType: string;
  requiredFlag: boolean;
  sort: number;
  isSystem?: boolean;
}

import { computed } from 'vue';

import draggable from 'vuedraggable';
import { Icon } from '@iconify/vue';

import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '#/utils/dict';

interface Props {
  pages: PageConfigItem[];
}

interface Emits {
  (e: 'update:pages', value: PageConfigItem[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性：获取当前页面配置列表
const pageConfigs = computed({
  get: () => props.pages,
  set: (value) => emit('update:pages', value),
});

/** 更新页面配置排序 */
const updatePageConfigsSort = () => {
  pageConfigs.value.forEach((config, index) => {
    config.sort = index; // 从0开始递增
  });
};

/** 删除页面配置 */
const handleDelete = (index: number) => {
  const pageConfig = pageConfigs.value[index];
  
  // 系统字段对应的页面配置不能删除
  if (pageConfig.isSystem) {
    return;
  }

  pageConfigs.value.splice(index, 1);
  updatePageConfigsSort();
};
</script>

<template>
  <div class="pages-table">
    <!-- 拖拽容器 -->
    <div class="drag-container">
      <!-- 表头行 -->
      <div class="table-header">
        <a-row :gutter="10" align="middle">
          <a-col :span="1">
            <span>排序</span>
          </a-col>
          <a-col :span="1">
            <span>序号</span>
          </a-col>
          <a-col :span="2">
            <span>字段名称</span>
          </a-col>
          <a-col :span="2">
            <span>字段文本</span>
          </a-col>
          <a-col :span="2">
            <span>查询条件</span>
          </a-col>
          <a-col :span="2">
            <span>表单显示</span>
          </a-col>
          <a-col :span="2">
            <span>列表显示</span>
          </a-col>
          <a-col :span="2">
            <span>只读</span>
          </a-col>
          <a-col :span="2">
            <span>排序</span>
          </a-col>
          <a-col :span="2">
            <span>查询类型</span>
          </a-col>
          <a-col :span="2">
            <span>控件类型</span>
          </a-col>
          <a-col :span="2">
            <span>必填</span>
          </a-col>
        </a-row>
      </div>

      <!-- 拖拽列表 -->
      <draggable
        v-model="pageConfigs"
        :item-key="(item: any, index: number) => item.columnName || `page-config-${index}`"
        handle=".drag-handle"
        ghost-class="sortable-ghost"
        @end="updatePageConfigsSort"
        :animation="300"
        class="drag-list"
      >
        <template #item="{ element, index }">
          <div class="drag-item">
            <a-row :gutter="10" class="mb-2" align="middle">
              <a-col :span="1">
                <a-button type="primary" size="small" class="drag-handle">
                  <Icon icon="ant-design:drag-outlined" />
                </a-button>
              </a-col>
              <a-col :span="1">
                <span>{{ index + 1 }}</span>
              </a-col>
              <a-col :span="2">
                <a-input
                  v-model:value="element.columnName"
                  placeholder="字段名称"
                  size="small"
                  :disabled="element.isSystem"
                />
              </a-col>
              <a-col :span="2">
                <a-input 
                  v-model:value="element.columnText" 
                  placeholder="字段文本" 
                  size="small"
                />
              </a-col>
              <a-col :span="2">
                <a-checkbox v-model:checked="element.queryFlag" />
              </a-col>
              <a-col :span="2">
                <a-checkbox v-model:checked="element.formShowFlag" />
              </a-col>
              <a-col :span="2">
                <a-checkbox v-model:checked="element.listShowFlag" />
              </a-col>
              <a-col :span="2">
                <a-checkbox v-model:checked="element.readonlyFlag" />
              </a-col>
              <a-col :span="2">
                <a-checkbox v-model:checked="element.sortFlag" />
              </a-col>
              <a-col :span="2">
                <a-select 
                  v-model:value="element.queryType" 
                  placeholder="查询类型" 
                  size="small"
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.INFRA_ONLINE_QUERY_TYPE)"
                    :key="dict.value || dict.label"
                    :value="dict.value"
                  >
                    {{ dict.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="2">
                <a-select 
                  v-model:value="element.componentType" 
                  placeholder="控件类型" 
                  size="small"
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_ONLINE_COMPONENT_TYPE)"
                    :key="dict.value || dict.label"
                    :value="dict.value"
                  >
                    {{ dict.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="2">
                <a-checkbox v-model:checked="element.requiredFlag" />
              </a-col>
            </a-row>
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>

<style scoped>
.pages-table {
  height: 100%;
}

.drag-container {
  margin-top: 10px;
}

.table-header {
  background-color: #f5f7fa;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
}

.table-header span {
  font-weight: bold;
  color: #606266;
  font-size: 12px;
}

.drag-list {
  min-height: 50px;
}

.drag-item {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.drag-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.sortable-ghost {
  opacity: 0.5;
  background: #f0f9ff;
}

.drag-handle {
  cursor: move;
}

.drag-handle:hover {
  background-color: #409eff;
}
</style>
