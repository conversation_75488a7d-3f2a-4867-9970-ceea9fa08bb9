<script lang="ts" setup>
import type { OnlineTablePageDTO } from '#/api/infra/onlineTable';

import { computed, ref } from 'vue';

import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

interface Props {
  pages: OnlineTablePageDTO[];
}

interface Emits {
  (e: 'update:pages', value: OnlineTablePageDTO[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表格引用
const tableRef = ref();

// 拖拽实例
const sortableInstance = ref<any>(null);

// 表格列定义
const tableColumns = [
  {
    title: '排序',
    dataIndex: 'drag',
    width: 60,
    fixed: 'left',
  },
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    fixed: 'left',
  },
  {
    title: '页面名称',
    dataIndex: 'pageName',
    width: 200,
  },
  {
    title: '页面类型',
    dataIndex: 'pageType',
    width: 120,
  },
  {
    title: '页面配置',
    dataIndex: 'pageConfig',
    width: 100,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'actions',
    width: 150,
  },
];

// 计算属性：获取当前页面列表
const pages = computed({
  get: () => props.pages,
  set: (value) => emit('update:pages', value),
});

// 配置弹窗相关
const configModalVisible = ref(false);
const currentPageConfig = ref('');
const currentPageIndex = ref(-1);

/** 添加页面 */
const handleAdd = () => {
  const newPage: OnlineTablePageDTO = {
    pageName: '',
    pageType: 1,
    pageConfig: '{}',
    sort: pages.value.length + 1,
  };

  pages.value.push(newPage);
};

/** 编辑配置 */
const handleEditConfig = (record: OnlineTablePageDTO, index: number) => {
  currentPageIndex.value = index;
  currentPageConfig.value = record.pageConfig || '{}';
  configModalVisible.value = true;
};

/** 保存配置 */
const handleSaveConfig = () => {
  try {
    // 验证JSON格式
    JSON.parse(currentPageConfig.value);

    if (currentPageIndex.value >= 0) {
      pages.value[currentPageIndex.value].pageConfig = currentPageConfig.value;
    }

    configModalVisible.value = false;
    message.success('配置保存成功');
  } catch {
    message.error('配置格式错误，请检查JSON格式');
  }
};

/** 取消配置 */
const handleCancelConfig = () => {
  configModalVisible.value = false;
  currentPageConfig.value = '';
  currentPageIndex.value = -1;
};

/** 格式化配置 */
const handleFormatConfig = () => {
  try {
    const parsed = JSON.parse(currentPageConfig.value);
    currentPageConfig.value = JSON.stringify(parsed, null, 2);
    message.success('格式化成功');
  } catch {
    message.error('JSON格式错误，无法格式化');
  }
};

/** 验证配置 */
const handleValidateConfig = () => {
  try {
    JSON.parse(currentPageConfig.value);
    message.success('JSON格式正确');
  } catch (error) {
    message.error(`JSON格式错误：${error.message}`);
  }
};

/** 上移页面 */
const handleMoveUp = (index: number) => {
  if (index > 0) {
    const temp = pages.value[index];
    pages.value[index] = pages.value[index - 1];
    pages.value[index - 1] = temp;

    // 更新排序
    pages.value.forEach((page, idx) => {
      page.sort = idx + 1;
    });
  }
};

/** 下移页面 */
const handleMoveDown = (index: number) => {
  if (index < pages.value.length - 1) {
    const temp = pages.value[index];
    pages.value[index] = pages.value[index + 1];
    pages.value[index + 1] = temp;

    // 更新排序
    pages.value.forEach((page, idx) => {
      page.sort = idx + 1;
    });
  }
};

/** 删除页面 */
const handleDelete = (index: number) => {
  pages.value.splice(index, 1);

  // 更新排序
  pages.value.forEach((page, idx) => {
    page.sort = idx + 1;
  });
};
</script>

<template>
  <div class="pages-table">
    <div class="mb-4">
      <a-button type="primary" @click="handleAdd">
        <Icon icon="ant-design:plus-outlined" />
        添加页面
      </a-button>
    </div>

    <a-table
      ref="tableRef"
      :columns="tableColumns"
      :data-source="pages"
      :pagination="false"
      :scroll="{ y: 400 }"
      row-key="pageId"
      size="small"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'drag'">
          <Icon
            icon="ant-design:drag-outlined"
            class="drag-handle cursor-move text-gray-400 hover:text-gray-600"
          />
        </template>

        <template v-else-if="column.dataIndex === 'index'">
          {{ index + 1 }}
        </template>

        <template v-else-if="column.dataIndex === 'pageName'">
          <a-input
            v-model:value="record.pageName"
            placeholder="请输入页面名称"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'pageType'">
          <a-select
            v-model:value="record.pageType"
            placeholder="请选择页面类型"
            size="small"
            style="width: 100%"
          >
            <a-select-option :value="1">列表页</a-select-option>
            <a-select-option :value="2">表单页</a-select-option>
            <a-select-option :value="3">详情页</a-select-option>
          </a-select>
        </template>

        <template v-else-if="column.dataIndex === 'pageConfig'">
          <a-button
            type="link"
            size="small"
            @click="handleEditConfig(record, index)"
          >
            配置
          </a-button>
        </template>

        <template v-else-if="column.dataIndex === 'sort'">
          <a-input-number
            v-model:value="record.sort"
            :min="1"
            :max="999"
            size="small"
            style="width: 100%"
          />
        </template>

        <template v-else-if="column.dataIndex === 'actions'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="handleMoveUp(index)"
              :disabled="index === 0"
            >
              上移
            </a-button>
            <a-button
              type="link"
              size="small"
              @click="handleMoveDown(index)"
              :disabled="index === pages.length - 1"
            >
              下移
            </a-button>
            <a-button
              type="link"
              size="small"
              danger
              @click="handleDelete(index)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 页面配置弹窗 -->
    <a-modal
      v-model:open="configModalVisible"
      title="页面配置"
      width="800px"
      @ok="handleSaveConfig"
      @cancel="handleCancelConfig"
    >
      <div class="config-editor">
        <a-textarea
          v-model:value="currentPageConfig"
          placeholder="请输入页面配置JSON"
          :rows="20"
          style="font-family: Monaco, Menlo, Consolas, monospace"
        />
      </div>

      <template #footer>
        <a-space>
          <a-button @click="handleCancelConfig">取消</a-button>
          <a-button type="primary" @click="handleSaveConfig">保存</a-button>
          <a-button @click="handleFormatConfig">格式化</a-button>
          <a-button @click="handleValidateConfig">验证</a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<style scoped>
.pages-table {
  height: 100%;
}

.config-editor {
  margin: 16px 0;
}
</style>
