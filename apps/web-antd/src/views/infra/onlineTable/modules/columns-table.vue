<script lang="ts" setup>
import type { OnlineTableColumnDTO } from '#/api/infra/onlineTable';

import { computed, ref } from 'vue';

import draggable from 'vuedraggable';
import { Icon } from '@iconify/vue';

import { DICT_TYPE, getStrDictOptions } from '#/utils/dict';

interface Props {
  columns: OnlineTableColumnDTO[];
  dictTypeOptions: any[];
}

interface Emits {
  (e: 'update:columns', value: OnlineTableColumnDTO[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性：获取当前字段列表
const columns = computed({
  get: () => props.columns,
  set: (value) => emit('update:columns', value),
});

/** 更新字段排序 */
const updateColumnsSort = () => {
  columns.value.forEach((col, index) => {
    col.sort = index + 1;
  });
};

/** 添加字段 */
const handleAdd = () => {
  const newColumn: OnlineTableColumnDTO = {
    columnName: `field_${columns.value.length + 1}`,
    columnComment: `字段${columns.value.length + 1}`,
    columnType: 'VARCHAR',
    columnLength: 255,
    columnScale: 0,
    isNullable: 1,
    isPrimaryKey: 0,
    isAutoIncrement: 0,
    defaultValue: '',
    sort: columns.value.length + 1,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
    dictType: '',
  };

  columns.value.push(newColumn);
};

/** 删除字段 */
const handleDelete = (index: number) => {
  const column = columns.value[index];

  if (!column) {
    return;
  }

  columns.value.splice(index, 1);
  updateColumnsSort();
};

/** 处理字段名称变化 */
const handleColumnNameChange = (index: number, newColumnName: string) => {
  // 可以在这里添加同步逻辑
  console.log('字段名称变化:', index, newColumnName);
};

/** 处理字段备注变化 */
const handleColumnCommentChange = (index: number, newColumnComment: string) => {
  // 可以在这里添加同步逻辑
  console.log('字段备注变化:', index, newColumnComment);
};
</script>

<template>
  <div class="columns-table">
    <!-- 操作按钮 -->
    <div class="mb-4">
      <a-button type="primary" @click="handleAdd">
        <Icon icon="ant-design:plus-outlined" />
        新增字段
      </a-button>
    </div>

    <!-- 拖拽容器 -->
    <div class="drag-container">
      <!-- 表头行 -->
      <div class="table-header">
        <a-row :gutter="10" align="middle">
          <a-col :span="1">
            <span>排序</span>
          </a-col>
          <a-col :span="1">
            <span>序号</span>
          </a-col>
          <a-col :span="3">
            <span>字段名称</span>
          </a-col>
          <a-col :span="3">
            <span>字段备注</span>
          </a-col>
          <a-col :span="2">
            <span>字段类型</span>
          </a-col>
          <a-col :span="2">
            <span>字段长度</span>
          </a-col>
          <a-col :span="2">
            <span>是否主键</span>
          </a-col>
          <a-col :span="2">
            <span>允许空值</span>
          </a-col>
          <a-col :span="2">
            <span>自动递增</span>
          </a-col>
          <a-col :span="3">
            <span>字典类型</span>
          </a-col>
          <a-col :span="3">
            <span>操作</span>
          </a-col>
        </a-row>
      </div>

      <!-- 拖拽列表 -->
      <draggable
        v-model="columns"
        :item-key="(item: any, index: number) => item.columnName || `column-${index}`"
        handle=".drag-handle"
        ghost-class="sortable-ghost"
        @end="updateColumnsSort"
        :animation="300"
        class="drag-list"
      >
        <template #item="{ element, index }">
          <div class="drag-item">
            <a-row :gutter="10" class="mb-2" align="middle">
              <a-col :span="1">
                <a-button type="primary" size="small" class="drag-handle">
                  <Icon icon="ant-design:drag-outlined" />
                </a-button>
              </a-col>
              <a-col :span="1">
                <span>{{ index + 1 }}</span>
              </a-col>
              <a-col :span="3">
                <a-input
                  v-model:value="element.columnName"
                  placeholder="请输入字段名称"
                  size="small"
                  @blur="handleColumnNameChange(index, element.columnName)"
                />
              </a-col>
              <a-col :span="3">
                <a-input
                  v-model:value="element.columnComment"
                  placeholder="请输入字段备注"
                  size="small"
                  @blur="handleColumnCommentChange(index, element.columnComment)"
                />
              </a-col>
              <a-col :span="2">
                <a-select 
                  v-model:value="element.columnType" 
                  placeholder="请选择字段类型" 
                  size="small"
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_ONLINE_COLUMN_TYPE) || []"
                    :key="dict.value || dict.label"
                    :value="dict.value"
                  >
                    {{ dict.label }}({{ dict.value }})
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="2">
                <a-input-number 
                  v-model:value="element.columnLength" 
                  :min="0" 
                  size="small"
                  style="width: 100%" 
                />
              </a-col>
              <a-col :span="2">
                <a-checkbox
                  v-model:checked="element.isPrimaryKey"
                  :checked-value="1"
                  :un-checked-value="0"
                />
              </a-col>
              <a-col :span="2">
                <a-checkbox
                  v-model:checked="element.isNullable"
                  :checked-value="1"
                  :un-checked-value="0"
                />
              </a-col>
              <a-col :span="2">
                <a-checkbox
                  v-model:checked="element.isAutoIncrement"
                  :checked-value="1"
                  :un-checked-value="0"
                />
              </a-col>
              <a-col :span="3">
                <a-select
                  v-model:value="element.dictType"
                  placeholder="请选择字典类型"
                  size="small"
                  allow-clear
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="dict in dictTypeOptions || []"
                    :key="dict.type || dict.name"
                    :value="dict.type"
                  >
                    {{ dict.name }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="3">
                <a-button
                  type="link"
                  danger
                  size="small"
                  @click="handleDelete(index)"
                >
                  删除
                </a-button>
              </a-col>
            </a-row>
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>

<style scoped>
.columns-table {
  height: 100%;
}

.drag-container {
  margin-top: 10px;
}

.table-header {
  background-color: #f5f7fa;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
}

.table-header span {
  font-weight: bold;
  color: #606266;
  font-size: 12px;
}

.drag-list {
  min-height: 50px;
}

.drag-item {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.drag-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.sortable-ghost {
  opacity: 0.5;
  background: #f0f9ff;
}

.drag-handle {
  cursor: move;
}

.drag-handle:hover {
  background-color: #409eff;
}
</style>
