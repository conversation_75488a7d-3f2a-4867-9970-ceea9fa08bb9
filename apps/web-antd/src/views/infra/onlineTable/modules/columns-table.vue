<script lang="ts" setup>
import type { OnlineTableColumnDTO } from '#/api/infra/onlineTable';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { useSortable } from '@vueuse/integrations/useSortable';
import { Icon } from '@iconify/vue';

import { columnTypeOptions, componentTypeOptions } from '../data';

interface Props {
  columns: OnlineTableColumnDTO[];
  dictTypeOptions: any[];
}

interface Emits {
  (e: 'update:columns', value: OnlineTableColumnDTO[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表格引用
const tableRef = ref();

// 拖拽实例
const sortableInstance = ref<any>(null);

// 表格列定义
const tableColumns = [
  {
    title: '排序',
    dataIndex: 'drag',
    width: 60,
    fixed: 'left',
  },
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    fixed: 'left',
  },
  {
    title: '字段名',
    dataIndex: 'columnName',
    width: 120,
  },
  {
    title: '字段备注',
    dataIndex: 'columnComment',
    width: 120,
  },
  {
    title: '字段类型',
    dataIndex: 'columnType',
    width: 100,
  },
  {
    title: '长度',
    dataIndex: 'columnLength',
    width: 80,
  },
  {
    title: '小数位',
    dataIndex: 'columnScale',
    width: 80,
  },
  {
    title: '允许空',
    dataIndex: 'isNullable',
    width: 80,
  },
  {
    title: '主键',
    dataIndex: 'isPrimaryKey',
    width: 60,
  },
  {
    title: '自增',
    dataIndex: 'isAutoIncrement',
    width: 60,
  },
  {
    title: '默认值',
    dataIndex: 'defaultValue',
    width: 100,
  },
  {
    title: '组件类型',
    dataIndex: 'componentType',
    width: 120,
  },
  {
    title: '字典类型',
    dataIndex: 'dictType',
    width: 120,
  },
  {
    title: '显示',
    dataIndex: 'showFlag',
    width: 60,
  },
  {
    title: '查询',
    dataIndex: 'queryFlag',
    width: 60,
  },
  {
    title: '编辑',
    dataIndex: 'editFlag',
    width: 60,
  },
  {
    title: '列表',
    dataIndex: 'listFlag',
    width: 60,
  },
  {
    title: '表单',
    dataIndex: 'formFlag',
    width: 60,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'actions',
    width: 150,
    fixed: 'right',
  },
];

// 计算属性：获取当前字段列表
const columns = computed({
  get: () => props.columns,
  set: (value) => emit('update:columns', value),
});

/** 初始化拖拽排序 */
const initSortable = () => {
  nextTick(() => {
    const tableElement = tableRef.value?.$el?.querySelector('.ant-table-tbody');
    if (tableElement && columns.value.length > 0) {
      // 停止现有实例
      if (sortableInstance.value) {
        sortableInstance.value.stop();
        sortableInstance.value = null;
      }

      // 使用 @vueuse/integrations/useSortable
      const sortableRef = useSortable(tableElement, columns.value, {
        animation: 300,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        handle: '.drag-handle',
        onUpdate: (evt) => {
          console.log('字段拖拽更新:', evt);
          // useSortable 会自动处理数组重排，我们只需要更新序号
          nextTick(() => {
            updateColumnsSort();
          });
        }
      });

      // 保存实例引用
      sortableInstance.value = sortableRef;
    }
  });
};

/** 更新字段排序 */
const updateColumnsSort = () => {
  columns.value.forEach((col, index) => {
    col.sort = index + 1;
  });
};

/** 添加字段 */
const handleAdd = () => {
  const newColumn: OnlineTableColumnDTO = {
    columnName: '',
    columnComment: '',
    columnType: 'VARCHAR',
    columnLength: 255,
    columnScale: 0,
    isNullable: 1,
    isPrimaryKey: 0,
    isAutoIncrement: 0,
    defaultValue: '',
    sort: columns.value.length + 1,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  };

  columns.value.push(newColumn);
  
  // 重新初始化拖拽
  nextTick(() => {
    initSortable();
  });
};

/** 删除字段 */
const handleDelete = (index: number) => {
  columns.value.splice(index, 1);
  updateColumnsSort();

  // 重新初始化拖拽
  nextTick(() => {
    initSortable();
  });
};

/** 初始化系统字段 */
const handleInitSystemColumns = () => {
  const systemColumns: OnlineTableColumnDTO[] = [
    {
      columnName: 'id',
      columnComment: '主键ID',
      columnType: 'BIGINT',
      columnLength: 20,
      columnScale: 0,
      isNullable: 0,
      isPrimaryKey: 1,
      isAutoIncrement: 1,
      defaultValue: '',
      sort: 1,
      showFlag: 0,
      queryFlag: 0,
      editFlag: 0,
      listFlag: 0,
      formFlag: 0,
      componentType: 'input',
    },
    {
      columnName: 'creator',
      columnComment: '创建者',
      columnType: 'VARCHAR',
      columnLength: 64,
      columnScale: 0,
      isNullable: 0,
      isPrimaryKey: 0,
      isAutoIncrement: 0,
      defaultValue: '',
      sort: 998,
      showFlag: 0,
      queryFlag: 0,
      editFlag: 0,
      listFlag: 0,
      formFlag: 0,
      componentType: 'input',
    },
    {
      columnName: 'create_time',
      columnComment: '创建时间',
      columnType: 'BIGINT',
      columnLength: 20,
      columnScale: 0,
      isNullable: 0,
      isPrimaryKey: 0,
      isAutoIncrement: 0,
      defaultValue: '',
      sort: 999,
      showFlag: 1,
      queryFlag: 0,
      editFlag: 0,
      listFlag: 1,
      formFlag: 0,
      componentType: 'datetime',
    },
    {
      columnName: 'updater',
      columnComment: '更新者',
      columnType: 'VARCHAR',
      columnLength: 64,
      columnScale: 0,
      isNullable: 0,
      isPrimaryKey: 0,
      isAutoIncrement: 0,
      defaultValue: '',
      sort: 1000,
      showFlag: 0,
      queryFlag: 0,
      editFlag: 0,
      listFlag: 0,
      formFlag: 0,
      componentType: 'input',
    },
    {
      columnName: 'update_time',
      columnComment: '更新时间',
      columnType: 'BIGINT',
      columnLength: 20,
      columnScale: 0,
      isNullable: 0,
      isPrimaryKey: 0,
      isAutoIncrement: 0,
      defaultValue: '',
      sort: 1001,
      showFlag: 1,
      queryFlag: 0,
      editFlag: 0,
      listFlag: 1,
      formFlag: 0,
      componentType: 'datetime',
    },
    {
      columnName: 'deleted',
      columnComment: '是否删除',
      columnType: 'BIT',
      columnLength: 1,
      columnScale: 0,
      isNullable: 0,
      isPrimaryKey: 0,
      isAutoIncrement: 0,
      defaultValue: "b'0'",
      sort: 1002,
      showFlag: 0,
      queryFlag: 0,
      editFlag: 0,
      listFlag: 0,
      formFlag: 0,
      componentType: 'switch',
    },
  ];

  // 添加系统字段到现有字段列表
  columns.value.push(...systemColumns);

  // 重新排序
  columns.value.forEach((col, index) => {
    col.sort = index + 1;
  });

  // 重新初始化拖拽
  nextTick(() => {
    initSortable();
  });
};

// 监听字段变化，重新初始化拖拽
watch(
  () => columns.value.length,
  () => {
    nextTick(() => {
      initSortable();
    });
  },
  { immediate: true }
);

// 组件挂载后初始化拖拽
onMounted(() => {
  initSortable();
});
</script>

<template>
  <div class="columns-table">
    <div class="mb-4 flex justify-between">
      <a-button type="primary" @click="handleAdd">
        <Icon icon="ant-design:plus-outlined" />
        添加字段
      </a-button>
      <a-button @click="handleInitSystemColumns">
        初始化系统字段
      </a-button>
    </div>

    <a-table
      ref="tableRef"
      :columns="tableColumns"
      :data-source="columns"
      :pagination="false"
      :scroll="{ x: 1500, y: 400 }"
      row-key="columnId"
      size="small"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'drag'">
          <Icon 
            icon="ant-design:drag-outlined" 
            class="drag-handle cursor-move text-gray-400 hover:text-gray-600"
          />
        </template>
        
        <template v-else-if="column.dataIndex === 'index'">
          {{ index + 1 }}
        </template>

        <template v-else-if="column.dataIndex === 'columnName'">
          <a-input
            v-model:value="record.columnName"
            placeholder="请输入字段名"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'columnComment'">
          <a-input
            v-model:value="record.columnComment"
            placeholder="请输入字段备注"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'columnType'">
          <a-select
            v-model:value="record.columnType"
            placeholder="请选择字段类型"
            size="small"
            style="width: 100%"
          >
            <a-select-option
              v-for="option in columnTypeOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </template>

        <template v-else-if="column.dataIndex === 'columnLength'">
          <a-input-number
            v-model:value="record.columnLength"
            :min="0"
            :max="9999"
            size="small"
            style="width: 100%"
          />
        </template>

        <template v-else-if="column.dataIndex === 'columnScale'">
          <a-input-number
            v-model:value="record.columnScale"
            :min="0"
            :max="30"
            size="small"
            style="width: 100%"
          />
        </template>

        <template v-else-if="column.dataIndex === 'isNullable'">
          <a-switch
            v-model:checked="record.isNullable"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'isPrimaryKey'">
          <a-switch
            v-model:checked="record.isPrimaryKey"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'isAutoIncrement'">
          <a-switch
            v-model:checked="record.isAutoIncrement"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'defaultValue'">
          <a-input
            v-model:value="record.defaultValue"
            placeholder="默认值"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'componentType'">
          <a-select
            v-model:value="record.componentType"
            placeholder="请选择组件类型"
            size="small"
            style="width: 100%"
          >
            <a-select-option
              v-for="option in componentTypeOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </template>

        <template v-else-if="column.dataIndex === 'dictType'">
          <a-select
            v-model:value="record.dictType"
            placeholder="请选择字典类型"
            size="small"
            style="width: 100%"
            allow-clear
          >
            <a-select-option
              v-for="option in dictTypeOptions"
              :key="option.type"
              :value="option.type"
            >
              {{ option.name }}
            </a-select-option>
          </a-select>
        </template>

        <template v-else-if="column.dataIndex === 'showFlag'">
          <a-switch
            v-model:checked="record.showFlag"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'queryFlag'">
          <a-switch
            v-model:checked="record.queryFlag"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'editFlag'">
          <a-switch
            v-model:checked="record.editFlag"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'listFlag'">
          <a-switch
            v-model:checked="record.listFlag"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'formFlag'">
          <a-switch
            v-model:checked="record.formFlag"
            :checked-value="1"
            :un-checked-value="0"
            size="small"
          />
        </template>

        <template v-else-if="column.dataIndex === 'sort'">
          <a-input-number
            v-model:value="record.sort"
            :min="1"
            :max="999"
            size="small"
            style="width: 100%"
          />
        </template>

        <template v-else-if="column.dataIndex === 'actions'">
          <a-space>
            <a-button
              type="link"
              size="small"
              danger
              @click="handleDelete(index)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style scoped>
.columns-table {
  height: 100%;
}

/* 拖拽样式 */
:deep(.sortable-ghost) {
  opacity: 0.5;
  background: #f0f0f0;
}

:deep(.sortable-chosen) {
  background: #e6f7ff;
}

:deep(.sortable-drag) {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.drag-handle {
  cursor: move;
}
</style>
