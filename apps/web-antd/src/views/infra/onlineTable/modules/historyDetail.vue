<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { formatDateTime } from '@vben/utils';

import { DictTag } from '#/components/dict-tag';
import { DICT_TYPE } from '#/utils';

// 响应式数据
const currentVersion = ref<OnlineTableVO>();

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '';
  return formatDateTime(timestamp);
};

// 打开弹窗
const open = (version: OnlineTableVO) => {
  currentVersion.value = version;
  modalApi.open();
};

const [Modal, modalApi] = useVbenModal();

// 暴露方法
defineExpose({
  open,
});
</script>

<template>
  <Modal
    :title="`版本详情 - v${currentVersion?.version || 1}`"
    :loading="false"
    class="w-[90%]"
    :show-ok="false"
    :show-cancel="true"
    cancel-text="关闭"
  >
    <div v-if="currentVersion" class="detail-container">
      <!-- 基本信息 -->
      <a-card :bordered="false" class="info-card">
        <template #title>
          <div class="card-header">
            <IconifyIcon icon="lucide:info" class="mr-2" />
            基本信息
          </div>
        </template>

        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="表格名称">
            {{ currentVersion.tableName }}
          </a-descriptions-item>
          <a-descriptions-item label="表格备注">
            {{ currentVersion.tableComment }}
          </a-descriptions-item>
          <a-descriptions-item label="版本号">
            <a-tag color="success">v{{ currentVersion.version || 1 }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="表单类型">
            <DictTag
              :type="DICT_TYPE.INFRA_ONLINE_TABLE_TYPE"
              :value="currentVersion.tableType"
            />
          </a-descriptions-item>
          <a-descriptions-item label="主键类型">
            <DictTag
              :type="DICT_TYPE.INFRA_ONLINE_TABLE_PRIVATE_KEY_TYPE"
              :value="currentVersion.privateKeyType"
            />
          </a-descriptions-item>
          <a-descriptions-item label="数据源ID">
            {{ currentVersion.datasourceId }}
          </a-descriptions-item>
          <a-descriptions-item label="同步状态">
            <a-tag :color="currentVersion.status === 1 ? 'success' : 'warning'">
              {{ currentVersion.status === 1 ? '已同步' : '未同步' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="多租户">
            <a-tag :color="currentVersion.tenant === 1 ? 'success' : 'default'">
              {{ currentVersion.tenant === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="分页查询">
            <a-tag :color="currentVersion.page === 1 ? 'success' : 'default'">
              {{ currentVersion.page === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="显示复选框">
            <a-tag
              :color="currentVersion.checkbox === 1 ? 'success' : 'default'"
            >
              {{ currentVersion.checkbox === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="显示滚动条">
            <a-tag
              :color="currentVersion.scrollBar === 1 ? 'success' : 'default'"
            >
              {{ currentVersion.scrollBar === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="表单风格">
            {{ currentVersion.tableFormStyle || 1 }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatTime(currentVersion.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建者">
            {{ currentVersion.creator || '系统' }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatTime(currentVersion.updateTime) }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 字段配置 -->
      <a-card :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <IconifyIcon icon="lucide:table" class="mr-2" />
            字段配置
            <a-tag color="blue" class="ml-2">
              共 {{ currentVersion.columns?.length || 0 }} 个字段
            </a-tag>
          </div>
        </template>

        <a-table
          :data-source="currentVersion.columns || []"
          :pagination="false"
          bordered
        >
          <a-table-column title="序号" width="60" align="center">
            <template #default="{ index }">
              {{ index + 1 }}
            </template>
          </a-table-column>
          <a-table-column
            data-index="columnName"
            title="字段名称"
            min-width="120"
          />
          <a-table-column
            data-index="columnComment"
            title="字段备注"
            min-width="120"
          />
          <a-table-column
            data-index="columnType"
            title="字段类型"
            width="100"
            align="center"
          >
            <template #default="{ record }">
              <DictTag
                :type="DICT_TYPE.INFRA_ONLINE_COLUMN_TYPE"
                :value="record.columnType"
              />
            </template>
          </a-table-column>
          <a-table-column
            data-index="columnLength"
            title="长度"
            width="80"
            align="center"
          />
          <a-table-column title="可为空" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.isNullable ? 'success' : 'error'">
                {{ record.isNullable ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="主键" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.isPrimaryKey ? 'error' : 'default'">
                {{ record.isPrimaryKey ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="同步DB" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.syncDb ? 'success' : 'warning'">
                {{ record.syncDb ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column data-index="dictType" title="字典类型" width="120" />
        </a-table>
      </a-card>

      <!-- 页面配置 -->
      <a-card :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <IconifyIcon icon="lucide:settings" class="mr-2" />
            页面配置
            <a-tag color="blue" class="ml-2">
              共 {{ currentVersion.pageConfigs?.length || 0 }} 个配置
            </a-tag>
          </div>
        </template>

        <a-table
          :data-source="currentVersion.pageConfigs || []"
          :pagination="false"
          bordered
        >
          <a-table-column title="序号" width="60" align="center">
            <template #default="{ index }">
              {{ index + 1 }}
            </template>
          </a-table-column>
          <a-table-column
            data-index="columnName"
            title="字段名称"
            min-width="120"
          />
          <a-table-column
            data-index="columnText"
            title="显示文本"
            min-width="120"
          />
          <a-table-column
            data-index="componentType"
            title="控件类型"
            width="100"
            align="center"
          >
            <template #default="{ record }">
              <DictTag
                :type="DICT_TYPE.INFRA_ONLINE_COMPONENT_TYPE"
                :value="record.componentType"
              />
            </template>
          </a-table-column>
          <a-table-column
            data-index="queryType"
            title="查询类型"
            width="100"
            align="center"
          >
            <template #default="{ record }">
              <DictTag
                :type="DICT_TYPE.INFRA_ONLINE_QUERY_TYPE"
                :value="record.queryType"
              />
            </template>
          </a-table-column>
          <a-table-column title="查询字段" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.queryFlag ? 'success' : 'default'">
                {{ record.queryFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="表单显示" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.formShowFlag ? 'success' : 'default'">
                {{ record.formShowFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="列表显示" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.listShowFlag ? 'success' : 'default'">
                {{ record.listShowFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="只读" width="60" align="center">
            <template #default="{ record }">
              <a-tag :color="record.readonlyFlag ? 'warning' : 'success'">
                {{ record.readonlyFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="必填" width="60" align="center">
            <template #default="{ record }">
              <a-tag :color="record.requiredFlag ? 'error' : 'default'">
                {{ record.requiredFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column
            data-index="sort"
            title="排序"
            width="60"
            align="center"
          />
        </a-table>
      </a-card>

      <!-- SQL内容 -->
      <a-card v-if="currentVersion.sqlContent" :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <IconifyIcon icon="lucide:file-text" class="mr-2" />
            SQL内容
          </div>
        </template>

        <a-textarea
          :value="currentVersion.sqlContent"
          :rows="10"
          readonly
          class="sql-content"
        />
      </a-card>
    </div>
  </Modal>
</template>

<style lang="scss" scoped>
.detail-container {
  .info-card {
    .card-header {
      display: flex;
      align-items: center;
      font-weight: bold;
      color: #1890ff;
    }
  }

  .sql-content {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
  }
}

// 表格样式优化
:deep(.ant-table) {
  .ant-table-thead {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: bold;
    }
  }

  .ant-table-tbody {
    tr:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
