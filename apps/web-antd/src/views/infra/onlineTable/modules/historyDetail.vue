<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { ref } from 'vue';

import { formatDateTime } from '@vben/utils';

import { DictTag } from '#/components/dict-tag';
import { DICT_TYPE } from '#/utils';

// 响应式数据
const currentVersion = ref<OnlineTableVO>();
const visible = ref(false);

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '';
  return formatDateTime(timestamp);
};

// 打开弹窗
const open = (version: OnlineTableVO) => {
  currentVersion.value = version;
  visible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 暴露方法
defineExpose({
  open,
});
</script>

<template>
  <a-modal
    v-model:open="visible"
    :title="`版本详情 - v${currentVersion?.version || 1}`"
    width="95%"
    :destroy-on-close="true"
    :mask-closable="true"
  >
    <template #footer>
      <a-button @click="handleClose">关闭</a-button>
    </template>
    <div v-if="currentVersion" class="detail-container">
      <!-- 基本信息 -->
      <a-card :bordered="false" class="info-card">
        <template #title>
          <div class="card-header">
            <a-radio checked class="mr-2" />
            基本信息
          </div>
        </template>

        <a-descriptions :column="3" bordered size="small">
          <a-descriptions-item label="表格名称">
            {{ currentVersion.tableName }}
          </a-descriptions-item>
          <a-descriptions-item label="表格备注">
            {{ currentVersion.tableComment }}
          </a-descriptions-item>
          <a-descriptions-item label="版本号">
            <a-tag color="blue">v{{ currentVersion.version || 1 }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="表单类型">
            <DictTag
              :type="DICT_TYPE.INFRA_ONLINE_TABLE_TYPE"
              :value="currentVersion.tableType"
            />
          </a-descriptions-item>
          <a-descriptions-item label="主键类型">
            <DictTag
              :type="DICT_TYPE.INFRA_ONLINE_TABLE_PRIVATE_KEY_TYPE"
              :value="currentVersion.privateKeyType"
            />
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatTime(currentVersion.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="数据源ID">
            {{ currentVersion.datasourceId }}
          </a-descriptions-item>
          <a-descriptions-item label="多租户">
            <a-tag :color="currentVersion.tenant === 1 ? 'success' : 'default'">
              {{ currentVersion.tenant === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatTime(currentVersion.updateTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="同步状态">
            <a-tag :color="currentVersion.status === 1 ? 'success' : 'warning'">
              {{ currentVersion.status === 1 ? '已同步' : '未同步' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="分页查询">
            <a-tag :color="currentVersion.page === 1 ? 'success' : 'default'">
              {{ currentVersion.page === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建者">
            {{ currentVersion.creator || 'admin' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 字段配置 -->
      <a-card :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <a-radio checked class="mr-2" />
            字段配置
            <span class="ml-2 text-gray-500">
              共 {{ currentVersion.columns?.length || 0 }} 个字段
            </span>
          </div>
        </template>

        <a-table
          :data-source="currentVersion.columns || []"
          :pagination="false"
          bordered
          size="small"
        >
          <a-table-column title="序号" width="60" align="center">
            <template #default="{ index }">
              {{ index + 1 }}
            </template>
          </a-table-column>
          <a-table-column
            data-index="columnName"
            title="字段名称"
            width="120"
          />
          <a-table-column
            data-index="columnComment"
            title="字段备注"
            width="120"
          />
          <a-table-column
            data-index="columnType"
            title="字段类型"
            width="100"
            align="center"
          >
            <template #default="{ record }">
              <DictTag
                :type="DICT_TYPE.INFRA_ONLINE_COLUMN_TYPE"
                :value="record.columnType"
              />
            </template>
          </a-table-column>
          <a-table-column
            data-index="columnLength"
            title="长度"
            width="80"
            align="center"
          />
          <a-table-column title="可为空" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.isNullable ? 'success' : 'error'">
                {{ record.isNullable ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="主键" width="60" align="center">
            <template #default="{ record }">
              <a-tag :color="record.isPrimaryKey ? 'error' : 'default'">
                {{ record.isPrimaryKey ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="同步DB" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.syncDb ? 'success' : 'warning'">
                {{ record.syncDb ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
        </a-table>
      </a-card>

      <!-- 页面配置 -->
      <a-card :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <a-radio checked class="mr-2" />
            页面配置
            <span class="ml-2 text-gray-500">
              共 {{ currentVersion.pageConfigs?.length || 0 }} 个配置
            </span>
          </div>
        </template>

        <a-table
          :data-source="currentVersion.pageConfigs || []"
          :pagination="false"
          bordered
          size="small"
        >
          <a-table-column title="序号" width="60" align="center">
            <template #default="{ index }">
              {{ index + 1 }}
            </template>
          </a-table-column>
          <a-table-column
            data-index="columnName"
            title="字段名称"
            width="120"
          />
          <a-table-column
            data-index="columnText"
            title="显示文本"
            width="120"
          />
          <a-table-column
            data-index="componentType"
            title="控件类型"
            width="100"
            align="center"
          >
            <template #default="{ record }">
              <DictTag
                :type="DICT_TYPE.INFRA_ONLINE_COMPONENT_TYPE"
                :value="record.componentType"
              />
            </template>
          </a-table-column>
          <a-table-column title="查询类型" width="100" align="center">
            <template #default="{ record }">
              <DictTag
                :type="DICT_TYPE.INFRA_ONLINE_QUERY_TYPE"
                :value="record.queryType"
              />
            </template>
          </a-table-column>
          <a-table-column title="查询字段" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.queryFlag ? 'success' : 'default'">
                {{ record.queryFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="表单显示" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.formShowFlag ? 'success' : 'default'">
                {{ record.formShowFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="列表显示" width="80" align="center">
            <template #default="{ record }">
              <a-tag :color="record.listShowFlag ? 'success' : 'default'">
                {{ record.listShowFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="只读" width="60" align="center">
            <template #default="{ record }">
              <a-tag :color="record.readonlyFlag ? 'warning' : 'success'">
                {{ record.readonlyFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="必填" width="60" align="center">
            <template #default="{ record }">
              <a-tag :color="record.requiredFlag ? 'error' : 'default'">
                {{ record.requiredFlag ? '是' : '否' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="排序" width="60" align="center">
            <template #default="{ record }">
              {{ record.sort || 0 }}
            </template>
          </a-table-column>
        </a-table>
      </a-card>

      <!-- SQL内容 -->
      <a-card v-if="currentVersion.sqlContent" :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <a-radio checked class="mr-2" />
            SQL内容
          </div>
        </template>

        <a-textarea
          :value="currentVersion.sqlContent"
          :rows="10"
          readonly
          class="sql-content"
        />
      </a-card>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
.detail-container {
  .info-card {
    .card-header {
      display: flex;
      align-items: center;
      font-weight: bold;
      color: #1890ff;
    }
  }

  .sql-content {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
  }
}

// 表格样式优化
:deep(.ant-table) {
  .ant-table-thead {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: bold;
    }
  }

  .ant-table-tbody {
    tr:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
