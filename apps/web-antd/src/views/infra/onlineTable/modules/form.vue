<script lang="ts" setup>
import type {
  OnlineTableColumnDTO,
  OnlineTableSaveDTO,
} from '#/api/infra/onlineTable';

import { ref, watch } from 'vue';
import { Codemirror } from 'vue-codemirror';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import {
  createOnlineTable,
  parseSqlToTableStructure,
  updateOnlineTable,
} from '#/api/infra/onlineTable';
import { getSimpleDictTypeList } from '#/api/system/dict/type';
import { $t } from '#/locales';

import ColumnsTable from './columns-table.vue';
import PagesTable from './pages-table.vue';

// 页面配置项类型（字段的页面配置）
interface PageConfigItem {
  columnName: string;
  columnText: string;
  queryFlag: boolean;
  formShowFlag: boolean;
  listShowFlag: boolean;
  readonlyFlag: boolean;
  sortFlag: boolean;
  queryType: string;
  componentType: string;
  requiredFlag: boolean;
  sort: number;
  isSystem?: boolean;
}

const emit = defineEmits(['success']);

// 表单数据
const formData = ref<OnlineTableSaveDTO>({
  tableName: '',
  tableComment: '',
  sqlContent: '',
  tableType: 1,
  privateKeyType: 1,
  datasourceId: '',
  status: 0,
  tenant: 0,
  page: 1,
  checkbox: 0,
  scrollBar: 0,
  tableFormStyle: 0,
  version: 1,
  columns: [],
  pageConfigs: [],
});

// 字段的页面配置
const fieldPageConfigs = ref<PageConfigItem[]>([]);

// 数据源选项
const dataSourceOptions = ref<any[]>([]);

// 字典类型选项
const dictTypeOptions = ref<any[]>([]);

// 加载状态
const loading = ref(false);

// 标签页
const activeTab = ref('columns');

// 基本信息表单schema
const baseFormSchema = ref<any[]>([
  {
    fieldName: 'tableName',
    label: '表格名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入表格名称(英文)',
    },
    rules: 'required',
    prefixIcon: '*',
  },
  {
    fieldName: 'tableComment',
    label: '表格备注',
    component: 'Input',
    componentProps: {
      placeholder: '请输入表格备注',
    },
    rules: 'required',
    prefixIcon: '*',
  },
  {
    fieldName: 'tableType',
    label: '主键类型',
    component: 'Select',
    componentProps: {
      placeholder: '自增',
      options: [
        { label: '自增', value: 1 },
        { label: 'UUID', value: 2 },
        { label: '雪花算法', value: 3 },
      ],
    },
    rules: 'required',
    prefixIcon: '*',
  },
  {
    fieldName: 'datasourceId',
    label: '数据源',
    component: 'Select',
    componentProps: {
      placeholder: '请选择数据源',
      options: dataSourceOptions.value.map((item) => ({
        label: item.name,
        value: item.id,
      })),
    },
    rules: 'required',
    prefixIcon: '*',
  },
]);

// 基本信息表单
// 我们仍然需要baseFormApi，但不需要BaseForm组件
const [, baseFormApi] = useVbenForm({
  schema: baseFormSchema.value,
  wrapperClass: 'grid grid-cols-1 md:grid-cols-3 gap-4',
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  showDefaultActions: false,
});

// 开关状态
const displayActivityEnabled = ref(true);

// SQL行号
const sqlLineNumber = ref(1);

// 默认系统字段
const getDefaultSystemColumns = (): OnlineTableColumnDTO[] => [
  {
    columnName: 'deleted',
    columnComment: '是否删除',
    columnType: 'bit',
    columnLength: 1,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 0,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'creator',
    columnComment: '创建者',
    columnType: 'varchar',
    columnLength: 64,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 1,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'create_time',
    columnComment: '创建时间（时间戳）',
    columnType: 'bigint',
    columnLength: 20,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 2,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'updater',
    columnComment: '更新者',
    columnType: 'varchar',
    columnLength: 64,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 3,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'update_time',
    columnComment: '更新时间（时间戳）',
    columnType: 'bigint',
    columnLength: 20,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 4,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
];

// 初始化默认字段
const initDefaultColumns = () => {
  formData.value.columns = getDefaultSystemColumns();
  // 同步生成页面配置
  syncPageConfigsFromColumns();
};

// 从字段配置同步页面配置
const syncPageConfigsFromColumns = () => {
  fieldPageConfigs.value = formData.value.columns.map((col, index) => ({
    columnName: col.columnName,
    columnText: col.columnComment || col.columnName,
    queryFlag: false, // 系统字段默认不作为查询条件
    formShowFlag: false, // 系统字段默认不在表单中显示
    listShowFlag: true, // 在列表中显示
    readonlyFlag: true, // 系统字段设为只读
    sortFlag: false,
    queryType: '1', // 精确查询
    componentType: 'input',
    requiredFlag: col.isNullable === 0,
    sort: index,
    isSystem: true, // 标记为系统字段
  }));
};

// 获取数据源列表
const getDataSourceList = async () => {
  try {
    const res = await getDataSourceConfigList();
    dataSourceOptions.value = res || [];

    // 更新表单schema中的数据源选项
    const datasourceField = baseFormSchema.value.find(
      (item) => item.fieldName === 'datasourceId',
    );
    if (datasourceField && datasourceField.componentProps) {
      datasourceField.componentProps.options = dataSourceOptions.value.map(
        (item) => ({
          label: item.name,
          value: item.id,
        }),
      );
    }
  } catch (error) {
    console.error('获取数据源列表失败', error);
  }
};

// 获取字典类型列表
const getDictTypeList = async () => {
  try {
    const res = await getSimpleDictTypeList();
    dictTypeOptions.value = res || [];
  } catch (error) {
    console.error('获取字典类型列表失败', error);
  }
};

// 解析SQL
const handleParseSql = async () => {
  if (!formData.value.datasourceId || !formData.value.sqlContent) {
    message.warning('请先选择数据源并输入SQL');
    return;
  }

  try {
    loading.value = true;
    const res = await parseSqlToTableStructure({
      datasourceId: formData.value.datasourceId,
      sqlContent: formData.value.sqlContent,
    });

    if (res) {
      // 更新表名
      if (res.tableName && !formData.value.tableName) {
        formData.value.tableName = res.tableName;
        await baseFormApi.setFieldValue('tableName', res.tableName);
      }

      // 更新字段信息，保留系统字段，添加解析出的字段
      if (res.columns && res.columns.length > 0) {
        const systemColumns = getDefaultSystemColumns();
        const parsedColumns = res.columns.map((col, index) => ({
          ...col,
          sort: systemColumns.length + index, // 继续从系统字段后面开始排序
        }));

        formData.value.columns = [...systemColumns, ...parsedColumns];
        syncPageConfigsFromColumns();
        message.success('SQL解析成功');
      }
    }
  } catch (error) {
    console.error('解析SQL失败', error);
    message.error('解析SQL失败');
  } finally {
    loading.value = false;
  }
};

// 监听表单数据变化，同步到基本信息表单
watch(
  () => formData.value,
  async (newData) => {
    await baseFormApi.setValues(newData);
  },
  { deep: true },
);

// 监听基本信息表单变化，同步到表单数据
watch(
  () => baseFormApi.getValues(),
  (newValues) => {
    Object.assign(formData.value, newValues);
  },
  { deep: true },
);

// 弹窗配置
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    try {
      // 验证基本信息表单
      const { valid } = await baseFormApi.validate();
      if (!valid) {
        return;
      }

      // 验证字段配置
      if (formData.value.columns.length === 0) {
        message.warning('请至少配置一个字段');
        return;
      }

      modalApi.lock();

      // 准备提交数据
      const submitData = {
        ...formData.value,
        pageConfigs: fieldPageConfigs.value.map((config, index) => ({
          pageName: config.columnName,
          pageType: 1,
          pageConfig: JSON.stringify(config),
          sort: index,
        })),
      };

      // 提交表单
      await (formData.value.tableId
        ? updateOnlineTable(submitData)
        : createOnlineTable(submitData));

      // 关闭并提示
      await modalApi.close();
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
    } catch (error) {
      console.error('保存失败', error);
      message.error('保存失败');
    } finally {
      modalApi.unlock();
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      // 初始化数据
      initDefaultColumns();
      await Promise.all([getDataSourceList(), getDictTypeList()]);
    }
  },
});

// 暴露方法
defineExpose({
  openModal: modalApi.open,
});
</script>

<template>
  <Modal title="新增表单" fullscreen :loading="loading">
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="mb-6">
        <h3 class="mb-4 text-lg font-medium">基本信息</h3>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
          <!-- 表格名称 -->
          <div class="flex flex-col">
            <label class="mb-1"
              ><span class="text-red-500">*</span> 表格名称</label
            >
            <a-input
              v-model:value="formData.tableName"
              placeholder="请输入表格名称(英文)"
            />
          </div>

          <!-- 表格备注 -->
          <div class="flex flex-col">
            <label class="mb-1"
              ><span class="text-red-500">*</span> 表格备注</label
            >
            <a-input
              v-model:value="formData.tableComment"
              placeholder="请输入表格备注"
            />
          </div>

          <!-- 表格类型 -->
          <div class="flex flex-col">
            <label class="mb-1"
              ><span class="text-red-500">*</span> 表格类型</label
            >
            <a-select v-model:value="formData.tableType" placeholder="单表">
              <a-select-option :value="1">单表</a-select-option>
            </a-select>
          </div>

          <!-- 主键类型 -->
          <div class="flex flex-col">
            <label class="mb-1"
              ><span class="text-red-500">*</span> 主键类型</label
            >
            <a-select
              v-model:value="formData.privateKeyType"
              placeholder="自增"
            >
              <a-select-option :value="1">自增</a-select-option>
              <a-select-option :value="2">UUID</a-select-option>
              <a-select-option :value="3">雪花算法</a-select-option>
            </a-select>
          </div>

          <!-- 数据源 -->
          <div class="flex flex-col">
            <label class="mb-1"
              ><span class="text-red-500">*</span> 数据源</label
            >
            <a-select
              v-model:value="formData.datasourceId"
              placeholder="请选择数据源"
            >
              <a-select-option
                v-for="item in dataSourceOptions"
                :key="item.id"
                :value="item.id"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 表单风格 -->
          <div class="flex flex-col">
            <label class="mb-1"
              ><span class="text-red-500">*</span> 表单风格</label
            >
            <a-select
              v-model:value="formData.tableFormStyle"
              placeholder="一列"
            >
              <a-select-option :value="0">一列</a-select-option>
            </a-select>
          </div>
        </div>

        <!-- 版本号 -->
        <div class="mt-4 flex items-center">
          <label class="mr-2">版本号</label>
          <div class="flex rounded border">
            <a-button
              class="border-0"
              @click="formData.version > 1 && formData.version--"
            >
              -
            </a-button>
            <div class="flex items-center px-4">{{ formData.version }}</div>
            <a-button class="border-0" @click="formData.version++">+</a-button>
          </div>
          <div class="ml-2 text-sm text-gray-400">数据可能会被删除</div>
        </div>

        <!-- 开关选项 -->
        <div class="mt-4 grid grid-cols-2 gap-4">
          <div class="flex items-center">
            <label class="mr-2">显示复选框</label>
            <a-switch
              v-model:checked="formData.checkbox"
              :checked-value="1"
              :unchecked-value="0"
            />
          </div>
          <div class="flex items-center">
            <label class="mr-2">显示活动栏</label>
            <a-switch v-model:checked="displayActivityEnabled" />
          </div>
          <div class="flex items-center">
            <label class="mr-2">多租户</label>
            <a-switch
              v-model:checked="formData.tenant"
              :checked-value="1"
              :unchecked-value="0"
            />
          </div>
          <div class="flex items-center">
            <label class="mr-2">分页查询</label>
            <a-switch
              v-model:checked="formData.page"
              :checked-value="1"
              :unchecked-value="0"
            />
          </div>
        </div>
      </div>

      <!-- SQL内容 -->
      <div class="mb-6">
        <h3 class="mb-4 text-lg font-medium">SQL内容</h3>
        <div class="flex">
          <div
            class="w-16 border-b border-l border-t bg-gray-100 p-2 text-center"
          >
            {{ sqlLineNumber }}
          </div>
          <div class="flex-1 rounded-r-md border">
            <Codemirror
              v-model="formData.sqlContent"
              :extensions="[]"
              :style="{ height: '200px' }"
              placeholder="请输入SQL语句"
            />
          </div>
        </div>
        <div class="mt-2 flex justify-end">
          <a-button
            type="primary"
            @click="handleParseSql"
            :loading="loading"
            class="bg-blue-500"
          >
            解析SQL
          </a-button>
          <a-button class="ml-2" icon="fullscreen-outlined">
            <i class="fas fa-expand"></i>
          </a-button>
        </div>
      </div>

      <!-- 字段配置 -->
      <div>
        <a-tabs v-model:active-key="activeTab">
          <a-tab-pane key="columns" tab="字段配置">
            <ColumnsTable
              v-model:columns="formData.columns"
              :dict-type-options="dictTypeOptions"
            />
          </a-tab-pane>
          <a-tab-pane key="pages" tab="页面配置">
            <PagesTable v-model:pages="fieldPageConfigs" />
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 底部按钮 -->
      <div class="mt-4 border-t pt-4">
        <a-button class="mr-2">字段配置</a-button>
        <a-button type="primary" class="bg-blue-500">页面配置</a-button>
      </div>
    </div>
  </Modal>
</template>
