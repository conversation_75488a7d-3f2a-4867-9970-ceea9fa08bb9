<template>
  <Modal
    :title="getTitle"
    :loading="loading"
    class="w-[90%]"
  >
    <div class="flex h-full flex-col">
      <!-- 基本信息表单 -->
      <a-card class="mb-4" title="基本信息">
        <BaseForm @register="registerBaseForm" />
      </a-card>

      <!-- SQL编辑器 -->
      <a-card class="mb-4" title="建表SQL">
        <div class="flex items-start gap-4">
          <div class="flex-1">
            <div class="overflow-hidden rounded border border-gray-300">
              <!-- CodeMirror编辑器 -->
              <Codemirror
                v-if="useCodeMirror"
                v-model="formData.sqlContent"
                :style="{
                  width: '100%',
                  height: isFullscreen ? 'calc(100vh - 100px)' : '300px',
                }"
                placeholder="请输入建表SQL"
                :indent-with-tab="true"
                :tab-size="2"
              />

              <!-- 后备的textarea编辑器 -->
              <a-textarea
                v-else
                v-model:value="formData.sqlContent"
                :style="{
                  width: '100%',
                  height: isFullscreen ? 'calc(100vh - 100px)' : '300px',
                  resize: 'none',
                  fontFamily: 'Monaco, Menlo, Consolas, monospace',
                }"
                placeholder="请输入建表SQL"
                class="sql-editor"
              />
            </div>
          </div>
          <div class="flex flex-col gap-2">
            <a-button
              type="primary"
              @click="handleParseSql"
              :disabled="!formData.datasourceId || !formData.sqlContent"
            >
              解析SQL
            </a-button>
            <a-button @click="isFullscreen = !isFullscreen">
              {{ isFullscreen ? '退出全屏' : '全屏编辑' }}
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 标签页 -->
      <a-card class="flex-1" title="字段配置">
        <a-tabs v-model:activeKey="activeTab" class="h-full">
          <a-tab-pane key="columns" tab="字段信息">
            <ColumnsTable
              v-model:columns="formData.columns"
              :dict-type-options="dictTypeOptions"
            />
          </a-tab-pane>
          <a-tab-pane key="pages" tab="页面配置">
            <PagesTable v-model:pages="formData.pageConfigs" />
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import type { OnlineTableSaveDTO, OnlineTableVO } from '#/api/infra/onlineTable';

import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';
import { Codemirror } from 'vue-codemirror';

import { useVbenForm } from '#/adapter/form';
import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import {
  createOnlineTable,
  getOnlineTable,
  parseSqlToTableStructure,
  updateOnlineTable,
} from '#/api/infra/onlineTable';
import { getSimpleDictTypeList } from '#/api/system/dict/type';
import { $t } from '#/locales';

import { useFormBaseSchema } from '../data';
import ColumnsTable from './columns-table.vue';
import PagesTable from './pages-table.vue';

const emit = defineEmits(['success']);

// 表单数据
const formData = ref<OnlineTableSaveDTO>();

// 数据源选项
const dataSourceOptions = ref<any[]>([]);

// 字典类型选项
const dictTypeOptions = ref<any[]>([]);

// 全屏状态
const isFullscreen = ref(false);

// 是否使用CodeMirror（如果出错则回退到textarea）
const useCodeMirror = ref(true);

// 标签页
const activeTab = ref('columns');

// 加载状态
const loading = ref(false);

// 表单标题
const getTitle = computed(() => {
  return formData.value?.tableId
    ? $t('ui.actionTitle.edit', ['表单'])
    : $t('ui.actionTitle.create', ['表单']);
});

// 基本信息表单
const [BaseForm, baseFormApi] = useVbenForm({
  schema: useFormBaseSchema(),
  wrapperClass: 'grid grid-cols-1 md:grid-cols-3 gap-4',
  commonConfig: {
    labelWidth: 80,
  },
  showDefaultActions: false,
});

// 监听表单数据变化，同步到基本信息表单
watch(
  () => formData.value,
  (newVal) => {
    if (newVal) {
      baseFormApi.setValues(newVal);
    }
  },
  { deep: true, immediate: true },
);

// 监听基本信息表单变化，同步到表单数据
watch(
  () => baseFormApi.getValues(),
  (newVal) => {
    if (newVal) {
      Object.assign(formData.value, newVal);
    }
  },
  { deep: true },
);

/** 获取数据源列表 */
const getDataSourceList = async () => {
  try {
    const res = await getDataSourceConfigList();
    dataSourceOptions.value = res || [];
    
    // 更新表单schema的数据源选项
    const schema = useFormBaseSchema();
    const datasourceField = schema.find(item => item.fieldName === 'datasourceId');
    if (datasourceField && datasourceField.componentProps) {
      datasourceField.componentProps.options = dataSourceOptions.value.map(item => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取数据源列表失败', error);
  }
};

/** 获取字典类型列表 */
const getDictTypeList = async () => {
  try {
    const res = await getSimpleDictTypeList();
    dictTypeOptions.value = res || [];
  } catch (error) {
    console.error('获取字典类型列表失败', error);
  }
};

/** 解析SQL */
const handleParseSql = async () => {
  if (!formData.value.datasourceId || !formData.value.sqlContent) {
    message.warning('请先选择数据源并输入SQL');
    return;
  }

  try {
    loading.value = true;
    const res = await parseSqlToTableStructure({
      datasourceId: formData.value.datasourceId,
      sqlContent: formData.value.sqlContent,
    });

    if (res) {
      // 更新表名
      if (res.tableName && !formData.value.tableName) {
        formData.value.tableName = res.tableName;
      }

      // 更新字段信息
      if (res.columns && res.columns.length > 0) {
        formData.value.columns = res.columns.map((col, index) => ({
          ...col,
          sort: index + 1,
          showFlag: 1,
          queryFlag: 0,
          editFlag: 1,
          listFlag: 1,
          formFlag: 1,
          componentType: getDefaultComponentType(col.columnType),
        }));
      }

      message.success('SQL解析成功');
      activeTab.value = 'columns';
    }
  } catch (error) {
    console.error('SQL解析失败', error);
    message.error('SQL解析失败');
  } finally {
    loading.value = false;
  }
};

/** 根据字段类型获取默认组件类型 */
const getDefaultComponentType = (columnType: string) => {
  const type = columnType.toUpperCase();
  if (type.includes('INT') || type.includes('DECIMAL') || type.includes('FLOAT') || type.includes('DOUBLE')) {
    return 'number';
  }
  if (type.includes('TEXT')) {
    return 'textarea';
  }
  if (type.includes('DATE')) {
    return 'date';
  }
  if (type.includes('TIME')) {
    return 'datetime';
  }
  return 'input';
};

// 注册弹窗
const registerBaseForm = baseFormApi.register;

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    try {
      // 验证基本信息表单
      await baseFormApi.validate();

      // 验证必填字段
      if (!formData.value?.tableName) {
        message.error('请输入表单名称');
        return;
      }
      if (!formData.value?.tableComment) {
        message.error('请输入表单备注');
        return;
      }
      if (!formData.value?.datasourceId) {
        message.error('请选择数据源');
        return;
      }
      if (!formData.value?.sqlContent) {
        message.error('请输入建表SQL');
        return;
      }

      modalApi.lock();

      if (formData.value.tableId) {
        await updateOnlineTable(formData.value);
        message.success('更新成功');
      } else {
        await createOnlineTable(formData.value);
        message.success('创建成功');
      }

      await modalApi.close();
      emit('success');
    } finally {
      modalApi.unlock();
    }
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      await Promise.all([getDataSourceList(), getDictTypeList()]);
    }
  },
  onSetData(data: { type: 'create' | 'update'; record?: OnlineTableVO }) {
    if (data.type === 'update' && data.record) {
      loading.value = true;
      getOnlineTable(data.record.tableId).then((res) => {
        formData.value = res;
        loading.value = false;
      }).catch(() => {
        loading.value = false;
      });
    } else {
      // 重置表单
      formData.value = {
        tableName: '',
        tableComment: '',
        sqlContent: '',
        tableType: 1,
        privateKeyType: 1,
        datasourceId: '',
        status: 0,
        tenant: 0,
        page: 1,
        checkbox: 0,
        scrollBar: 0,
        tableFormStyle: 0,
        version: 1,
        columns: [],
        pageConfigs: [],
      };
    }
  },
});
</script>
