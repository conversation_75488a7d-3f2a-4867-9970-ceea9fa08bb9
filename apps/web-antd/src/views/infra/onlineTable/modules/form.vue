<script lang="ts" setup>
import type {
  OnlineTableColumnDTO,
  OnlineTableSaveDTO,
  OnlineTableVO,
} from '#/api/infra/onlineTable';

import { computed, ref, watch } from 'vue';
import { Codemirror } from 'vue-codemirror';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import {
  createOnlineTable,
  getOnlineTable,
  parseSqlToTableStructure,
  updateOnlineTable,
} from '#/api/infra/onlineTable';
import { getSimpleDictTypeList } from '#/api/system/dict/type';
import { $t } from '#/locales';
import { DICT_TYPE } from '#/utils/dict';

import ColumnsTable from './columns-table.vue';
import PagesTable from './pages-table.vue';

// 页面配置项类型（字段的页面配置）
interface PageConfigItem {
  columnName: string;
  columnText: string;
  queryFlag: boolean;
  formShowFlag: boolean;
  listShowFlag: boolean;
  readonlyFlag: boolean;
  sortFlag: boolean;
  queryType: string;
  componentType: string;
  requiredFlag: boolean;
  sort: number;
  isSystem?: boolean;
}

const emit = defineEmits(['success']);

// 表单数据
const formData = ref<OnlineTableSaveDTO>({
  tableName: '',
  tableComment: '',
  sqlContent: '',
  tableType: 1,
  privateKeyType: 1,
  datasourceId: '',
  status: 0,
  tenant: 0,
  page: 1,
  checkbox: 0,
  scrollBar: 0,
  tableFormStyle: 0,
  version: 1,
  columns: [],
  pageConfigs: [],
});

// 字段的页面配置
const fieldPageConfigs = ref<PageConfigItem[]>([]);

// 数据源选项
const dataSourceOptions = ref<any[]>([]);

// 字典类型选项
const dictTypeOptions = ref<any[]>([]);

// 加载状态
const loading = ref(false);

// 标签页
const activeTab = ref('columns');

// 基本信息表单schema
const baseFormSchema = ref([
  {
    fieldName: 'tableName',
    label: '表单名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入表单名称',
    },
    rules: 'required',
  },
  {
    fieldName: 'tableComment',
    label: '表单备注',
    component: 'Input',
    componentProps: {
      placeholder: '请输入表单备注',
    },
    rules: 'required',
  },
  {
    fieldName: 'tableType',
    label: '表单类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择表单类型',
      options: [
        { label: '单表', value: 1 },
        { label: '主子表', value: 2 },
        { label: '树表', value: 3 },
      ],
    },
    rules: 'required',
  },
  {
    fieldName: 'privateKeyType',
    label: '主键类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择主键类型',
      options: [
        { label: '自增', value: 1 },
        { label: 'UUID', value: 2 },
        { label: '雪花算法', value: 3 },
      ],
    },
    rules: 'required',
  },
  {
    fieldName: 'datasourceId',
    label: '数据源',
    component: 'Select',
    componentProps: {
      placeholder: '请选择数据源',
      options: dataSourceOptions.value.map((item) => ({
        label: item.name,
        value: item.id,
      })),
    },
    rules: 'required',
  },
  {
    fieldName: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '开启', value: 0 },
        { label: '关闭', value: 1 },
      ],
    },
    rules: 'required',
  },
  {
    fieldName: 'tenant',
    label: '多租户',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否多租户',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
    rules: 'required',
  },
  {
    fieldName: 'page',
    label: '分页',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否分页',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
    rules: 'required',
  },
  {
    fieldName: 'checkbox',
    label: '复选框',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否显示复选框',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
  },
]);

// 基本信息表单
const [BaseForm, baseFormApi] = useVbenForm({
  schema: baseFormSchema,
  wrapperClass: 'grid grid-cols-1 md:grid-cols-3 gap-4',
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  showDefaultActions: false,
});

// 默认系统字段
const getDefaultSystemColumns = (): OnlineTableColumnDTO[] => [
  {
    columnName: 'deleted',
    columnComment: '是否删除',
    columnType: 'bit',
    columnLength: 1,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 0,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'creator',
    columnComment: '创建者',
    columnType: 'varchar',
    columnLength: 64,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 1,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'create_time',
    columnComment: '创建时间（时间戳）',
    columnType: 'bigint',
    columnLength: 20,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 2,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'updater',
    columnComment: '更新者',
    columnType: 'varchar',
    columnLength: 64,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 3,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
  {
    columnName: 'update_time',
    columnComment: '更新时间（时间戳）',
    columnType: 'bigint',
    columnLength: 20,
    columnScale: 0,
    isPrimaryKey: 0,
    isNullable: 0,
    isAutoIncrement: 0,
    dictType: '',
    sort: 4,
    showFlag: 1,
    queryFlag: 0,
    editFlag: 1,
    listFlag: 1,
    formFlag: 1,
    componentType: 'input',
  },
];

// 初始化默认字段
const initDefaultColumns = () => {
  formData.value.columns = getDefaultSystemColumns();
  // 同步生成页面配置
  syncPageConfigsFromColumns();
};

// 从字段配置同步页面配置
const syncPageConfigsFromColumns = () => {
  fieldPageConfigs.value = formData.value.columns.map((col, index) => ({
    columnName: col.columnName,
    columnText: col.columnComment || col.columnName,
    queryFlag: false, // 系统字段默认不作为查询条件
    formShowFlag: false, // 系统字段默认不在表单中显示
    listShowFlag: true, // 在列表中显示
    readonlyFlag: true, // 系统字段设为只读
    sortFlag: false,
    queryType: '1', // 精确查询
    componentType: 'input',
    requiredFlag: col.isNullable === 0,
    sort: index,
    isSystem: true, // 标记为系统字段
  }));
};

// 获取数据源列表
const getDataSourceList = async () => {
  try {
    const res = await getDataSourceConfigList();
    dataSourceOptions.value = res || [];

    // 更新表单schema中的数据源选项
    const datasourceField = baseFormSchema.value.find(
      (item) => item.fieldName === 'datasourceId',
    );
    if (datasourceField && datasourceField.componentProps) {
      datasourceField.componentProps.options = dataSourceOptions.value.map(
        (item) => ({
          label: item.name,
          value: item.id,
        }),
      );
    }
  } catch (error) {
    console.error('获取数据源列表失败', error);
  }
};

// 获取字典类型列表
const getDictTypeList = async () => {
  try {
    const res = await getSimpleDictTypeList();
    dictTypeOptions.value = res || [];
  } catch (error) {
    console.error('获取字典类型列表失败', error);
  }
};

// 解析SQL
const handleParseSql = async () => {
  if (!formData.value.datasourceId || !formData.value.sqlContent) {
    message.warning('请先选择数据源并输入SQL');
    return;
  }

  try {
    loading.value = true;
    const res = await parseSqlToTableStructure({
      datasourceId: formData.value.datasourceId,
      sqlContent: formData.value.sqlContent,
    });

    if (res) {
      // 更新表名
      if (res.tableName && !formData.value.tableName) {
        formData.value.tableName = res.tableName;
        await baseFormApi.setFieldValue('tableName', res.tableName);
      }

      // 更新字段信息，保留系统字段，添加解析出的字段
      if (res.columns && res.columns.length > 0) {
        const systemColumns = getDefaultSystemColumns();
        const parsedColumns = res.columns.map((col, index) => ({
          ...col,
          sort: systemColumns.length + index, // 继续从系统字段后面开始排序
        }));

        formData.value.columns = [...systemColumns, ...parsedColumns];
        syncPageConfigsFromColumns();
        message.success('SQL解析成功');
      }
    }
  } catch (error) {
    console.error('解析SQL失败', error);
    message.error('解析SQL失败');
  } finally {
    loading.value = false;
  }
};

// 监听表单数据变化，同步到基本信息表单
watch(
  () => formData.value,
  async (newData) => {
    await baseFormApi.setValues(newData);
  },
  { deep: true }
);

// 监听基本信息表单变化，同步到表单数据
watch(
  () => baseFormApi.getValues(),
  (newValues) => {
    Object.assign(formData.value, newValues);
  },
  { deep: true }
);

// 弹窗配置
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    try {
      // 验证基本信息表单
      const { valid } = await baseFormApi.validate();
      if (!valid) {
        return;
      }

      // 验证字段配置
      if (formData.value.columns.length === 0) {
        message.warning('请至少配置一个字段');
        return;
      }

      modalApi.lock();

      // 准备提交数据
      const submitData = {
        ...formData.value,
        pageConfigs: fieldPageConfigs.value.map((config, index) => ({
          pageName: config.columnName,
          pageType: 1,
          pageConfig: JSON.stringify(config),
          sort: index,
        })),
      };

      // 提交表单
      await (formData.value.tableId ? updateOnlineTable(submitData) : createOnlineTable(submitData));

      // 关闭并提示
      await modalApi.close();
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
    } catch (error) {
      console.error('保存失败', error);
      message.error('保存失败');
    } finally {
      modalApi.unlock();
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      // 初始化数据
      initDefaultColumns();
      await Promise.all([getDataSourceList(), getDictTypeList()]);
    }
  },
});

// 暴露方法
defineExpose({
  openModal: modalApi.open,
});
</script>

<template>
  <Modal
    title="在线表单配置"
    :width="1200"
    :height="800"
    :loading="loading"
  >
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4">基本信息</h3>
        <BaseForm />
      </div>

      <!-- SQL内容 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4">SQL内容</h3>
        <div class="border rounded-md">
          <Codemirror
            v-model="formData.sqlContent"
            :extensions="[]"
            :style="{ height: '200px' }"
            placeholder="请输入SQL语句"
          />
        </div>
        <div class="mt-2">
          <a-button type="primary" @click="handleParseSql" :loading="loading">
            解析SQL
          </a-button>
        </div>
      </div>

      <!-- 字段配置 -->
      <div>
        <a-tabs v-model:activeKey="activeTab">
          <a-tab-pane key="columns" tab="字段配置">
            <ColumnsTable
              v-model:columns="formData.columns"
              :dict-type-options="dictTypeOptions"
            />
          </a-tab-pane>
          <a-tab-pane key="pages" tab="页面配置">
            <PagesTable v-model:pages="fieldPageConfigs" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </Modal>
</template>
