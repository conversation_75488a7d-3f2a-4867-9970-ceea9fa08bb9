<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { formatDateTime } from '@vben/utils';

import { Modal as AModal, message } from 'ant-design-vue';

import { getHistoryVersions } from '#/api/infra/onlineTable';

// import CompareDialog from './historyCompare.vue';
// import DetailDialog from './historyDetail.vue';

// 响应式数据
const loading = ref(false);
const tableName = ref('');
const tableComment = ref('');
const historyList = ref<OnlineTableVO[]>([]);
const searchVersion = ref('');
const searchCreator = ref('');

// 组件引用
// const detailRef = ref();
// const compareRef = ref();

// 计算属性 - 过滤后的历史列表
const filteredHistoryList = computed(() => {
  let list = historyList.value;

  if (searchVersion.value) {
    list = list.filter((item) =>
      String(item.version || 1).includes(searchVersion.value),
    );
  }

  if (searchCreator.value) {
    list = list.filter((item) =>
      (item.creator || '')
        .toLowerCase()
        .includes(searchCreator.value.toLowerCase()),
    );
  }

  return list;
});

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '';
  return formatDateTime(timestamp);
};

// 加载历史版本列表
const loadHistoryList = async () => {
  if (!tableName.value) return;

  loading.value = true;
  try {
    const data = await getHistoryVersions(tableName.value);
    historyList.value = data || [];

    // 按版本号和创建时间降序排序
    historyList.value.sort((a, b) => {
      const versionA = a.version || 1;
      const versionB = b.version || 1;
      if (versionA !== versionB) {
        return versionB - versionA;
      }
      return (b.createTime || 0) - (a.createTime || 0);
    });
  } catch (error) {
    console.error('获取历史版本失败:', error);
    message.error('获取历史版本失败');
    historyList.value = [];
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

// 重置搜索
const resetSearch = () => {
  searchVersion.value = '';
  searchCreator.value = '';
};

// 查看详情
const handleViewDetail = (_item: OnlineTableVO) => {
  // detailRef.value.open(item);
  message.info('详情功能开发中');
};

// 版本对比
const handleCompare = (
  _oldVersion: OnlineTableVO,
  _currentVersion: OnlineTableVO,
) => {
  // compareRef.value.open(oldVersion, currentVersion);
  message.info('版本对比功能开发中');
};

// 恢复版本
const handleRestore = async (item: OnlineTableVO) => {
  try {
    AModal.confirm({
      title: '确认恢复',
      content: `确定要恢复到版本 v${item.version || 1} 吗？恢复后当前版本将被覆盖。`,
      okText: '确定恢复',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里需要调用恢复版本的API（如果后端提供）
          // await restoreVersion(item.tableId)

          message.success('版本恢复成功');
          await loadHistoryList();
        } catch (error) {
          console.error('恢复版本失败:', error);
          message.error('恢复版本失败');
        }
      },
    });
  } catch (error) {
    console.error('Modal error:', error);
  }
};

const [Modal, modalApi] = useVbenModal({
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      historyList.value = [];
      return;
    }

    // 获取传递的数据
    const data = modalApi.getData<{
      tableComment: string;
      tableName: string;
    }>();
    if (!data || !data.tableName) {
      message.error('缺少表名参数');
      return;
    }

    tableName.value = data.tableName;
    tableComment.value = data.tableComment || '';

    // 加载历史版本数据
    modalApi.lock();
    try {
      await loadHistoryList();
    } finally {
      modalApi.unlock();
    }
  },
});
</script>

<template>
  <Modal
    :title="`${tableComment} - 历史版本`"
    :loading="loading"
    class="w-[80%]"
    :show-ok="false"
    :show-cancel="true"
    cancel-text="关闭"
  >
    <div class="history-container">
      <!-- 搜索栏 -->
      <a-card :bordered="false" class="search-card">
        <a-form layout="inline" class="search-form">
          <a-form-item label="版本号">
            <a-input
              v-model:value="searchVersion"
              placeholder="请输入版本号"
              allow-clear
              style="width: 150px"
              @input="handleSearch"
            />
          </a-form-item>
          <a-form-item label="创建者">
            <a-input
              v-model:value="searchCreator"
              placeholder="请输入创建者"
              allow-clear
              style="width: 150px"
              @input="handleSearch"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <IconifyIcon icon="lucide:search" class="mr-1" />
              搜索
            </a-button>
            <a-button @click="resetSearch" class="ml-2">
              <IconifyIcon icon="lucide:refresh-cw" class="mr-1" />
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 历史版本列表 -->
      <a-card :bordered="false" class="mt-4">
        <a-spin :spinning="loading" class="history-list">
          <a-empty
            v-if="!loading && historyList.length === 0"
            description="暂无历史版本"
          />

          <div v-else class="version-timeline">
            <a-timeline>
              <a-timeline-item
                v-for="(item, index) in filteredHistoryList"
                :key="item.tableId"
                :color="index === 0 ? '#52c41a' : '#1890ff'"
              >
                <template #label>
                  <div class="timeline-label">
                    <div class="version-number">v{{ item.version || 1 }}</div>
                    <div class="version-time">
                      {{ formatTime(item.createTime) }}
                    </div>
                  </div>
                </template>

                <a-card
                  class="version-card"
                  :class="{ 'current-version': index === 0 }"
                >
                  <template #title>
                    <div class="version-header">
                      <div class="version-info">
                        <a-tag
                          :color="index === 0 ? 'success' : 'processing'"
                          class="version-tag"
                        >
                          v{{ item.version || 1 }}
                          <span v-if="index === 0" class="current-label">
                            （当前版本）
                          </span>
                        </a-tag>
                        <span class="creator">
                          创建者：{{ item.creator || '系统' }}
                        </span>
                      </div>
                      <div class="version-actions">
                        <a-button
                          type="primary"
                          size="small"
                          @click="handleViewDetail(item)"
                        >
                          查看详情
                        </a-button>
                        <a-button
                          v-if="index !== 0"
                          type="default"
                          size="small"
                          @click="handleCompare(item, historyList[0]!)"
                          class="ml-2"
                        >
                          与当前版本对比
                        </a-button>
                        <a-button
                          v-if="index !== 0"
                          type="default"
                          size="small"
                          @click="handleRestore(item)"
                          class="ml-2"
                        >
                          恢复此版本
                        </a-button>
                      </div>
                    </div>
                  </template>

                  <div class="version-content">
                    <a-descriptions :column="2" size="small">
                      <a-descriptions-item label="表格名称">
                        {{ item.tableName }}
                      </a-descriptions-item>
                      <a-descriptions-item label="表格备注">
                        {{ item.tableComment }}
                      </a-descriptions-item>
                      <a-descriptions-item label="表单类型">
                        <a-tag color="blue">{{ item.tableType || 1 }}</a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="主键类型">
                        <a-tag color="blue">
                          {{ item.privateKeyType || 1 }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="字段数量">
                        <a-tag color="blue">
                          {{ item.columns?.length || 0 }} 个字段
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="页面配置">
                        <a-tag color="blue">
                          {{ item.pageConfigs?.length || 0 }} 个配置
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="同步状态">
                        <a-tag
                          :color="item.status === 1 ? 'success' : 'warning'"
                        >
                          {{ item.status === 1 ? '已同步' : '未同步' }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="多租户">
                        <a-tag
                          :color="item.tenant === 1 ? 'success' : 'default'"
                        >
                          {{ item.tenant === 1 ? '是' : '否' }}
                        </a-tag>
                      </a-descriptions-item>
                    </a-descriptions>

                    <!-- 字段预览 -->
                    <div class="field-preview mt-4">
                      <a-divider orientation="left">字段预览</a-divider>
                      <div class="field-tags">
                        <a-tag
                          v-for="column in (item.columns || []).slice(0, 8)"
                          :key="column.columnId"
                          :color="column.isPrimaryKey ? 'red' : 'blue'"
                          class="field-tag"
                        >
                          {{ column.columnName }}
                          <span class="field-type">{{
                            column.columnType
                          }}</span>
                        </a-tag>
                        <a-tag
                          v-if="(item.columns || []).length > 8"
                          color="blue"
                          class="field-tag"
                        >
                          +{{ (item.columns || []).length - 8 }} 个字段
                        </a-tag>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-timeline-item>
            </a-timeline>
          </div>
        </a-spin>
      </a-card>
    </div>

    <!-- 详情弹窗 -->
    <!-- <DetailDialog ref="detailRef" /> -->

    <!-- 对比弹窗 -->
    <!-- <CompareDialog ref="compareRef" /> -->
  </Modal>
</template>

<style lang="scss" scoped>
.history-container {
  .search-card {
    margin-bottom: 16px;

    .search-form {
      margin-bottom: 0;
    }
  }

  .history-list {
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
  }

  .version-timeline {
    padding: 20px;

    .timeline-label {
      text-align: right;
      padding-right: 8px;
      min-width: 120px;
      margin-left: -20px;

      .version-number {
        font-size: 14px;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 4px;
      }

      .version-time {
        font-size: 12px;
        color: #666;
        white-space: nowrap;
      }
    }

    .version-card {
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.current-version {
        border: 2px solid #52c41a;
        box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
      }
    }

    .version-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .version-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .version-tag {
          font-weight: bold;

          .current-label {
            font-size: 12px;
            margin-left: 4px;
          }
        }

        .creator {
          color: #666;
          font-size: 14px;
        }
      }

      .version-actions {
        display: flex;
        gap: 8px;
      }
    }

    .version-content {
      .field-preview {
        .field-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .field-tag {
            .field-type {
              margin-left: 4px;
              opacity: 0.7;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .version-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;

    .version-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .field-tags {
    .field-tag {
      font-size: 12px !important;
    }
  }
}
</style>
