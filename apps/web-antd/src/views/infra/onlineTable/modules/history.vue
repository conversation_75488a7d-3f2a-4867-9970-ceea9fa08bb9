<template>
  <Modal
    title="历史版本"
    :loading="loading"
    class="w-[80%]"
    :show-ok="false"
    :show-cancel="true"
    cancel-text="关闭"
  >
    <div class="history-container">
      <div v-if="historyList.length === 0" class="empty-state">
        <a-empty description="暂无历史版本" />
      </div>
      <a-timeline v-else>
        <a-timeline-item
          v-for="(record, index) in historyList"
          :key="record.tableId"
          :color="record.status === 1 ? 'green' : 'blue'"
        >
          <div class="history-item">
            <div class="history-header">
              <div class="history-info">
                <span class="history-time">{{ formatDateTime(record.createTime) }}</span>
                <a-tag :color="record.status === 1 ? 'green' : 'orange'" class="ml-2">
                  {{ record.status === 1 ? '已同步' : '未同步' }}
                </a-tag>
              </div>
              <div class="history-actions">
                <a-button
                  type="link"
                  size="small"
                  @click="handleView(record)"
                >
                  查看详情
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="handleRestore(record)"
                  :disabled="record.status === 1"
                >
                  恢复版本
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  danger
                  @click="handleDelete(record)"
                  :disabled="record.status === 1"
                >
                  删除版本
                </a-button>
              </div>
            </div>
            <div class="history-content">
              <div class="history-details">
                <div class="detail-row">
                  <span class="detail-label">表单名称:</span>
                  <span class="detail-value">{{ record.tableName }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">表单备注:</span>
                  <span class="detail-value">{{ record.tableComment }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">版本号:</span>
                  <span class="detail-value">v{{ record.version || 1 }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">创建者:</span>
                  <span class="detail-value">{{ record.creator || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>
    
    <!-- 查看详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="版本详情"
      width="800px"
      :footer="null"
    >
      <div class="detail-container">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="表单名称">
            {{ currentRecord?.tableName }}
          </a-descriptions-item>
          <a-descriptions-item label="表单备注">
            {{ currentRecord?.tableComment }}
          </a-descriptions-item>
          <a-descriptions-item label="版本号">
            v{{ currentRecord?.version || 1 }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="currentRecord?.status === 1 ? 'green' : 'orange'">
              {{ currentRecord?.status === 1 ? '已同步' : '未同步' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(currentRecord?.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建者">
            {{ currentRecord?.creator }}
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>SQL内容</a-divider>
        <a-textarea
          :value="currentRecord?.sqlContent"
          :rows="10"
          readonly
          style="font-family: Monaco, Menlo, Consolas, monospace"
        />
        
        <a-divider>字段信息</a-divider>
        <a-table
          :columns="columnTableColumns"
          :data-source="currentRecord?.columns || []"
          :pagination="false"
          :scroll="{ y: 200 }"
          row-key="columnId"
          size="small"
        />
      </div>
    </a-modal>
  </Modal>
</template>

<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { formatDateTime } from '@vben/utils';

import { message, Modal, Timeline } from 'ant-design-vue';

import { deleteOnlineTable, getHistoryVersions } from '#/api/infra/onlineTable';

const emit = defineEmits(['success']);

// 数据
const loading = ref(false);
const historyList = ref<OnlineTableVO[]>([]);
const detailModalVisible = ref(false);
const currentRecord = ref<OnlineTableVO | null>(null);

// 移除表格列定义，改用时间线展示

// 字段表格列定义
const columnTableColumns = [
  {
    title: '字段名',
    dataIndex: 'columnName',
    width: 120,
  },
  {
    title: '字段备注',
    dataIndex: 'columnComment',
    width: 150,
  },
  {
    title: '字段类型',
    dataIndex: 'columnType',
    width: 100,
  },
  {
    title: '长度',
    dataIndex: 'columnLength',
    width: 80,
  },
  {
    title: '允许空',
    dataIndex: 'isNullable',
    width: 80,
    customRender: ({ record }) => record.isNullable ? '是' : '否',
  },
  {
    title: '主键',
    dataIndex: 'isPrimaryKey',
    width: 60,
    customRender: ({ record }) => record.isPrimaryKey ? '是' : '否',
  },
];

/** 获取历史版本列表 */
const getHistoryList = async (tableName: string) => {
  loading.value = true;
  try {
    const res = await getHistoryVersions(tableName);
    historyList.value = res || [];
  } catch (error) {
    console.error('获取历史版本失败', error);
    message.error('获取历史版本失败');
  } finally {
    loading.value = false;
  }
};

/** 查看详情 */
const handleView = (record: OnlineTableVO) => {
  currentRecord.value = record;
  detailModalVisible.value = true;
};

/** 恢复版本 */
const handleRestore = (record: OnlineTableVO) => {
  Modal.confirm({
    title: '确认恢复',
    content: `是否确认恢复到版本 v${record.version}？`,
    onOk: async () => {
      try {
        // 这里应该调用恢复版本的API
        // await restoreOnlineTableVersion(record.tableId);
        message.success('版本恢复成功');
        emit('success');
      } catch (error) {
        console.error('版本恢复失败', error);
        message.error('版本恢复失败');
      }
    },
  });
};

/** 删除版本 */
const handleDelete = (record: OnlineTableVO) => {
  Modal.confirm({
    title: '确认删除',
    content: `是否确认删除版本 v${record.version}？`,
    onOk: async () => {
      try {
        await deleteOnlineTable(record.tableId);
        message.success('版本删除成功');
        
        // 重新获取历史版本列表
        if (historyList.value.length > 0) {
          await getHistoryList(historyList.value[0].tableName);
        }
      } catch (error) {
        console.error('版本删除失败', error);
        message.error('版本删除失败');
      }
    },
  });
};

const [Modal, modalApi] = useVbenModal({
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      historyList.value = [];
      currentRecord.value = null;
      detailModalVisible.value = false;
      return;
    }

    // 获取传递的数据
    const data = modalApi.getData<{ tableName: string }>();
    if (!data || !data.tableName) {
      message.error('缺少表名参数');
      return;
    }

    // 加载历史版本数据
    modalApi.lock();
    try {
      await getHistoryList(data.tableName);
    } finally {
      modalApi.unlock();
    }
  },
});
</script>

<style scoped>
.history-container {
  height: 100%;
  max-height: 500px;
  overflow-y: auto;
}

.history-item {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-info {
  display: flex;
  align-items: center;
}

.history-time {
  font-weight: 500;
  color: #262626;
}

.history-actions {
  display: flex;
  gap: 8px;
}

.history-content {
  border-top: 1px solid #e8e8e8;
  padding-top: 12px;
}

.history-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-weight: 500;
  color: #8c8c8c;
  min-width: 80px;
  margin-right: 8px;
}

.detail-value {
  color: #262626;
  flex: 1;
}

.detail-container {
  max-height: 600px;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
