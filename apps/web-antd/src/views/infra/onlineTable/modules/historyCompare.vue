<script lang="ts" setup>
import type { OnlineTableVO } from '#/api/infra/onlineTable';

import { computed, ref } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { formatDateTime } from '@vben/utils';

// 响应式数据
const oldVersion = ref<OnlineTableVO>();
const currentVersion = ref<OnlineTableVO>();
const visible = ref(false);

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '';
  return formatDateTime(timestamp);
};

// 基本信息对比数据
const basicInfoCompareData = computed(() => {
  if (!oldVersion.value || !currentVersion.value) return [];

  const fields = [
    { key: 'tableName', label: '表格名称' },
    { key: 'tableComment', label: '表格备注' },
    { key: 'tableType', label: '表单类型' },
    { key: 'privateKeyType', label: '主键类型' },
    { key: 'datasourceId', label: '数据源ID' },
    { key: 'status', label: '同步状态' },
    { key: 'tenant', label: '多租户' },
    { key: 'page', label: '分页查询' },
    { key: 'checkbox', label: '显示复选框' },
    { key: 'scrollBar', label: '显示滚动条' },
    { key: 'tableFormStyle', label: '表单风格' },
  ];

  return fields.map((field) => {
    const oldValue = oldVersion.value![field.key as keyof OnlineTableVO];
    const currentValue =
      currentVersion.value![field.key as keyof OnlineTableVO];
    const isDifferent = oldValue !== currentValue;

    return {
      field: field.label,
      oldValue: String(oldValue || ''),
      currentValue: String(currentValue || ''),
      isDifferent,
    };
  });
});

// 字段对比数据
const fieldCompareData = computed(() => {
  if (!oldVersion.value || !currentVersion.value) return [];

  const oldColumns = oldVersion.value.columns || [];
  const currentColumns = currentVersion.value.columns || [];
  const compareData: any[] = [];

  // 获取所有字段名称
  const allColumnNames = new Set([
    ...currentColumns.map((col) => col.columnName),
    ...oldColumns.map((col) => col.columnName),
  ]);

  allColumnNames.forEach((columnName) => {
    const oldColumn = oldColumns.find((col) => col.columnName === columnName);
    const currentColumn = currentColumns.find(
      (col) => col.columnName === columnName,
    );

    let status = 'unchanged';
    const changes: string[] = [];

    if (!oldColumn && currentColumn) {
      status = 'added';
    } else if (oldColumn && !currentColumn) {
      status = 'deleted';
    } else if (oldColumn && currentColumn) {
      // 检查变更
      const fields = [
        'columnComment',
        'columnType',
        'columnLength',
        'isNullable',
        'isPrimaryKey',
        'syncDb',
        'dictType',
      ];
      fields.forEach((field) => {
        if (
          oldColumn[field as keyof typeof oldColumn] !==
          currentColumn[field as keyof typeof currentColumn]
        ) {
          changes.push(field);
          status = 'modified';
        }
      });
    }

    compareData.push({
      columnName,
      oldColumn,
      currentColumn,
      status,
      changes,
    });
  });

  return compareData;
});

// 页面配置对比数据
const pageCompareData = computed(() => {
  if (!oldVersion.value || !currentVersion.value) return [];

  const oldPages = oldVersion.value.pageConfigs || [];
  const currentPages = currentVersion.value.pageConfigs || [];
  const compareData: any[] = [];

  // 获取所有字段名称
  const allColumnNames = new Set([
    ...currentPages.map((page) => page.columnName),
    ...oldPages.map((page) => page.columnName),
  ]);

  allColumnNames.forEach((columnName) => {
    const oldPage = oldPages.find((page) => page.columnName === columnName);
    const currentPage = currentPages.find(
      (page) => page.columnName === columnName,
    );

    let status = 'unchanged';
    const changes: string[] = [];

    if (!oldPage && currentPage) {
      status = 'added';
    } else if (oldPage && !currentPage) {
      status = 'deleted';
    } else if (oldPage && currentPage) {
      // 检查变更
      const fields = [
        'columnText',
        'componentType',
        'queryType',
        'validateRule',
        'labelLength',
        'componentLength',
        'componentDefaultValue',
        'queryFlag',
        'formShowFlag',
        'listShowFlag',
        'readonlyFlag',
        'sortFlag',
        'sortType',
        'requiredFlag',
        'dictType',
      ];
      fields.forEach((field) => {
        if (
          oldPage[field as keyof typeof oldPage] !==
          currentPage[field as keyof typeof currentPage]
        ) {
          changes.push(field);
          status = 'modified';
        }
      });
    }

    compareData.push({
      columnName,
      oldPage,
      currentPage,
      status,
      changes,
    });
  });

  return compareData;
});

// 字段统计
const fieldStats = computed(() => {
  const stats = { added: 0, modified: 0, deleted: 0 };
  fieldCompareData.value.forEach((item) => {
    switch (item.status) {
      case 'added': {
        stats.added++;
        break;
      }
      case 'deleted': {
        {
          stats.deleted++;
          // No default
        }
        break;
      }
      case 'modified': {
        stats.modified++;
        break;
      }
    }
  });
  return stats;
});

// 页面配置统计
const pageStats = computed(() => {
  const stats = { added: 0, modified: 0, deleted: 0 };
  pageCompareData.value.forEach((item) => {
    switch (item.status) {
      case 'added': {
        stats.added++;
        break;
      }
      case 'deleted': {
        {
          stats.deleted++;
          // No default
        }
        break;
      }
      case 'modified': {
        stats.modified++;
        break;
      }
    }
  });
  return stats;
});

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'added': {
      return 'success';
    }
    case 'deleted': {
      return 'error';
    }
    case 'modified': {
      return 'warning';
    }
    default: {
      return 'default';
    }
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'added': {
      return '新增';
    }
    case 'deleted': {
      return '删除';
    }
    case 'modified': {
      return '修改';
    }
    default: {
      return '无变更';
    }
  }
};

// 打开弹窗
const open = (old: OnlineTableVO, current: OnlineTableVO) => {
  oldVersion.value = old;
  currentVersion.value = current;
  visible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 暴露方法
defineExpose({
  open,
});
</script>

<template>
  <a-modal
    v-model:open="visible"
    title="版本对比"
    width="95%"
    :footer="null"
    :destroy-on-close="false"
    :mask-closable="false"
  >
    <template #footer>
      <a-button @click="handleClose">关闭</a-button>
    </template>
    <div v-if="oldVersion && currentVersion" class="compare-container">
      <!-- 版本信息对比 -->
      <a-card :bordered="false" class="compare-header">
        <div class="version-compare-info">
          <div class="version-item old-version">
            <a-tag color="warning">
              v{{ oldVersion.version || 1 }} (历史版本)
            </a-tag>
            <div class="version-meta">
              <span>创建时间：{{ formatTime(oldVersion.createTime) }}</span>
              <span>创建者：{{ oldVersion.creator || '系统' }}</span>
            </div>
          </div>

          <div class="vs-divider">
            <IconifyIcon icon="lucide:arrow-right" />
          </div>

          <div class="version-item current-version">
            <a-tag color="success">
              v{{ currentVersion.version || 1 }} (当前版本)
            </a-tag>
            <div class="version-meta">
              <span>创建时间：{{ formatTime(currentVersion.createTime) }}</span>
              <span>创建者：{{ currentVersion.creator || '系统' }}</span>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 基本信息对比 -->
      <a-card :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <IconifyIcon icon="lucide:info" class="mr-2" />
            基本信息对比
          </div>
        </template>

        <div class="compare-table">
          <a-table
            :data-source="basicInfoCompareData"
            :pagination="false"
            bordered
          >
            <a-table-column
              data-index="field"
              title="字段"
              width="150"
              align="center"
            />
            <a-table-column title="历史版本" align="center">
              <template #default="{ record }">
                <div :class="{ 'diff-value': record.isDifferent }">
                  {{ record.oldValue }}
                </div>
              </template>
            </a-table-column>
            <a-table-column title="当前版本" align="center">
              <template #default="{ record }">
                <div :class="{ 'diff-value': record.isDifferent }">
                  {{ record.currentValue }}
                </div>
              </template>
            </a-table-column>
            <a-table-column title="状态" width="100" align="center">
              <template #default="{ record }">
                <a-tag :color="record.isDifferent ? 'warning' : 'success'">
                  {{ record.isDifferent ? '已变更' : '无变更' }}
                </a-tag>
              </template>
            </a-table-column>
          </a-table>
        </div>
      </a-card>

      <!-- 字段配置对比 -->
      <a-card :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <IconifyIcon icon="lucide:table" class="mr-2" />
            字段配置对比
            <div class="compare-stats">
              <a-tag color="success">新增: {{ fieldStats.added }}</a-tag>
              <a-tag color="warning">修改: {{ fieldStats.modified }}</a-tag>
              <a-tag color="error">删除: {{ fieldStats.deleted }}</a-tag>
            </div>
          </div>
        </template>

        <div class="compare-table">
          <a-table :data-source="fieldCompareData" :pagination="false" bordered>
            <a-table-column
              data-index="columnName"
              title="字段名称"
              width="120"
            />
            <a-table-column title="状态" width="80" align="center">
              <template #default="{ record }">
                <a-tag :color="getStatusType(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
            </a-table-column>
            <a-table-column title="历史版本" align="center">
              <template #default="{ record }">
                <div v-if="record.oldColumn" class="field-info">
                  <div>{{ record.oldColumn.columnComment }}</div>
                  <div class="field-meta">
                    {{ record.oldColumn.columnType }}({{
                      record.oldColumn.columnLength
                    }})
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </a-table-column>
            <a-table-column title="当前版本" align="center">
              <template #default="{ record }">
                <div v-if="record.currentColumn" class="field-info">
                  <div>{{ record.currentColumn.columnComment }}</div>
                  <div class="field-meta">
                    {{ record.currentColumn.columnType }}({{
                      record.currentColumn.columnLength
                    }})
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </a-table-column>
            <a-table-column title="变更详情" min-width="200">
              <template #default="{ record }">
                <div
                  v-if="record.changes && record.changes.length > 0"
                  class="changes-list"
                >
                  <a-tag
                    v-for="change in record.changes"
                    :key="change"
                    color="blue"
                    class="change-tag"
                  >
                    {{ change }}
                  </a-tag>
                </div>
                <span v-else class="empty-value">无变更</span>
              </template>
            </a-table-column>
          </a-table>
        </div>
      </a-card>

      <!-- 页面配置对比 -->
      <a-card :bordered="false" class="mt-4">
        <template #title>
          <div class="card-header">
            <IconifyIcon icon="lucide:settings" class="mr-2" />
            页面配置对比
            <div class="compare-stats">
              <a-tag color="success">新增: {{ pageStats.added }}</a-tag>
              <a-tag color="warning">修改: {{ pageStats.modified }}</a-tag>
              <a-tag color="error">删除: {{ pageStats.deleted }}</a-tag>
            </div>
          </div>
        </template>

        <div class="compare-table">
          <a-table :data-source="pageCompareData" :pagination="false" bordered>
            <a-table-column
              data-index="columnName"
              title="字段名称"
              width="120"
            />
            <a-table-column title="状态" width="80" align="center">
              <template #default="{ record }">
                <a-tag :color="getStatusType(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
            </a-table-column>
            <a-table-column title="历史版本" align="center">
              <template #default="{ record }">
                <div v-if="record.oldPage" class="page-info">
                  <div>{{ record.oldPage.columnText }}</div>
                  <div class="page-meta">
                    {{ record.oldPage.componentType }} |
                    {{ record.oldPage.queryType }}
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </a-table-column>
            <a-table-column title="当前版本" align="center">
              <template #default="{ record }">
                <div v-if="record.currentPage" class="page-info">
                  <div>{{ record.currentPage.columnText }}</div>
                  <div class="page-meta">
                    {{ record.currentPage.componentType }} |
                    {{ record.currentPage.queryType }}
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </a-table-column>
            <a-table-column title="变更详情" min-width="200">
              <template #default="{ record }">
                <div
                  v-if="record.changes && record.changes.length > 0"
                  class="changes-list"
                >
                  <a-tag
                    v-for="change in record.changes"
                    :key="change"
                    color="blue"
                    class="change-tag"
                  >
                    {{ change }}
                  </a-tag>
                </div>
                <span v-else class="empty-value">无变更</span>
              </template>
            </a-table-column>
          </a-table>
        </div>
      </a-card>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
.compare-container {
  .compare-header {
    .version-compare-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;

      .version-item {
        flex: 1;
        text-align: center;

        .version-meta {
          margin-top: 8px;
          font-size: 12px;
          color: #666;

          span {
            display: block;
            margin: 2px 0;
          }
        }
      }

      .vs-divider {
        margin: 0 20px;
        color: #1890ff;
      }
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    color: #1890ff;

    .compare-stats {
      display: flex;
      gap: 8px;
    }
  }

  .compare-table {
    .diff-value {
      background-color: #fff3cd;
      padding: 2px 4px;
      border-radius: 4px;
      border: 1px solid #ffeaa7;
    }

    .field-info,
    .page-info {
      .field-meta,
      .page-meta {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
      }
    }

    .empty-value {
      color: #999;
      font-style: italic;
    }

    .changes-list {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .change-tag {
        font-size: 11px;
      }
    }
  }
}

// 表格样式优化
:deep(.ant-table) {
  .ant-table-thead {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: bold;
    }
  }
}
</style>
