# 在线表单功能测试指南

## 测试环境准备

### 1. 数据库准备
执行以下SQL创建测试表：

```sql
-- 创建测试数据源（如果没有的话）
INSERT INTO `infra_data_source_config` (`name`, `url`, `username`, `password`, `creator`, `create_time`) 
VALUES ('测试数据源', '********************************', 'root', 'password', 'admin', UNIX_TIMESTAMP() * 1000);

-- 创建在线表单相关表
-- （执行之前提供的三个建表SQL）
```

### 2. 字典数据准备
在系统字典管理中添加以下字典类型：

```sql
-- 表单类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('表单类型', 'online_table_type', 0, '在线表单类型', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '单表', '1', 'online_table_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, '主子表', '2', 'online_table_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(3, '树表', '3', 'online_table_type', 0, 'admin', UNIX_TIMESTAMP() * 1000);

-- 主键类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('主键类型', 'online_table_private_key_type', 0, '在线表单主键类型', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '自增', '1', 'online_table_private_key_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, 'UUID', '2', 'online_table_private_key_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(3, '雪花算法', '3', 'online_table_private_key_type', 0, 'admin', UNIX_TIMESTAMP() * 1000);

-- 表单风格
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('表单风格', 'online_table_form_style', 0, '在线表单风格', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '默认', '0', 'online_table_form_style', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, '卡片', '1', 'online_table_form_style', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(3, '只读', '2', 'online_table_form_style', 0, 'admin', UNIX_TIMESTAMP() * 1000);

-- 字段类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('字段类型', 'online_column_type', 0, '在线表单字段类型', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '字符串', 'varchar', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, '整数', 'int', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(3, '长整数', 'bigint', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(4, '小数', 'decimal', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(5, '日期', 'date', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(6, '日期时间', 'datetime', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(7, '文本', 'text', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(8, '布尔', 'boolean', 'online_column_type', 0, 'admin', UNIX_TIMESTAMP() * 1000);

-- 控件类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('控件类型', 'online_component_type', 0, '在线表单控件类型', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '输入框', 'input', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, '密码框', 'password', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(3, '数字输入框', 'input-number', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(4, '文本域', 'textarea', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(5, '选择器', 'select', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(6, '多选选择器', 'multi-select', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(7, '单选框', 'radio', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(8, '复选框', 'checkbox', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(9, '开关', 'switch', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(10, '日期选择器', 'date-picker', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(11, '日期时间选择器', 'datetime-picker', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(12, '时间选择器', 'time-picker', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(13, '日期范围选择器', 'daterange-picker', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(14, '滑块', 'slider', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(15, '评分', 'rate', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(16, '颜色选择器', 'color-picker', 'online_component_type', 0, 'admin', UNIX_TIMESTAMP() * 1000);

-- 查询类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('查询类型', 'online_query_type', 0, '在线表单查询类型', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '精确查询', '1', 'online_query_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, '模糊查询', '2', 'online_query_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(3, '范围查询', '3', 'online_query_type', 0, 'admin', UNIX_TIMESTAMP() * 1000);

-- 排序类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('排序类型', 'online_sort_type', 0, '在线表单排序类型', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '正序', '1', 'online_sort_type', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, '逆序', '2', 'online_sort_type', 0, 'admin', UNIX_TIMESTAMP() * 1000);

-- 验证规则
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`) 
VALUES ('验证规则', 'online_validate_rule', 0, '在线表单验证规则', 'admin', UNIX_TIMESTAMP() * 1000);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `creator`, `create_time`) VALUES
(1, '无验证', '', 'online_validate_rule', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(2, '邮箱', '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', 'online_validate_rule', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(3, '手机号', '^1[3-9]\d{9}$', 'online_validate_rule', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(4, '身份证', '^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$', 'online_validate_rule', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(5, '数字', '^\d+$', 'online_validate_rule', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(6, '字母', '^[a-zA-Z]+$', 'online_validate_rule', 0, 'admin', UNIX_TIMESTAMP() * 1000),
(7, '字母数字', '^[a-zA-Z0-9]+$', 'online_validate_rule', 0, 'admin', UNIX_TIMESTAMP() * 1000);
```

## 测试步骤

### 1. 基础功能测试

#### 1.1 创建表单
1. 访问 `/infra/online-table`
2. 点击"新增"按钮
3. 填写基本信息：
   - 表格名称：`test_user`
   - 表格备注：`测试用户表`
   - 表单类型：选择"单表"
   - 主键类型：选择"自增"
   - 数据源：选择已配置的数据源
   - 表单风格：选择"默认"

#### 1.2 SQL解析测试
在建表SQL中输入：
```sql
CREATE TABLE `test_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态',
  `remark` text COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试用户表';
```

点击"解析SQL"按钮，验证是否正确解析出字段配置。

#### 1.3 字段配置测试
验证字段配置是否正确：
- 检查字段名称、备注、类型、长度
- 设置主键字段
- 配置字典类型（如状态字段）

#### 1.4 页面配置测试
配置页面显示：
- 设置查询条件字段
- 配置表单显示字段
- 设置列表显示字段
- 配置控件类型
- 设置验证规则

### 2. 预览功能测试

#### 2.1 同步数据库
1. 保存表单配置
2. 点击"同步DB"按钮
3. 验证数据库中是否创建了对应的表

#### 2.2 预览页面测试
1. 点击"预览"按钮
2. 验证页面是否正确显示
3. 测试查询功能
4. 测试新增功能
5. 测试编辑功能
6. 测试删除功能
7. 测试批量删除功能
8. 测试导出功能

### 3. 高级功能测试

#### 3.1 不同控件类型测试
创建包含各种控件类型的表单，测试：
- 输入框、密码框、数字输入框
- 文本域、选择器、多选选择器
- 单选框、复选框、开关
- 日期选择器、时间选择器、日期范围选择器
- 滑块、评分、颜色选择器

#### 3.2 验证规则测试
测试各种验证规则：
- 必填验证
- 邮箱格式验证
- 手机号格式验证
- 自定义正则表达式验证

#### 3.3 查询类型测试
测试不同查询类型：
- 精确查询
- 模糊查询
- 范围查询

## 预期结果

### 成功标准
1. 能够成功创建和配置在线表单
2. SQL解析功能正常工作
3. 字段配置和页面配置保存正确
4. 数据库同步功能正常
5. 预览页面能够正确显示和操作
6. 所有CRUD操作功能正常
7. 各种控件类型显示和交互正常
8. 验证规则生效
9. 查询、排序、分页功能正常
10. 导出功能正常

### 常见问题排查
1. 如果SQL解析失败，检查数据源配置是否正确
2. 如果预览页面显示异常，检查字段配置和页面配置是否正确
3. 如果CRUD操作失败，检查后端API是否正常
4. 如果控件显示异常，检查字典配置是否正确

## 性能测试

### 大数据量测试
1. 创建包含大量字段的表单（50+字段）
2. 测试大数据量的查询和分页（10000+记录）
3. 测试批量操作性能

### 并发测试
1. 多用户同时访问预览页面
2. 多用户同时进行CRUD操作
3. 验证数据一致性

## 总结

通过以上测试，可以验证在线表单开发功能的完整性和稳定性。如果所有测试都通过，说明功能开发成功，可以投入生产使用。
