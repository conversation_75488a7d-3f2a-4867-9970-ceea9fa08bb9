import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { getDataSourceConfigList } from '#/api/infra/data-source-config';
import { DICT_TYPE } from '#/utils/dict';

/** 列表的字段 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'tableId',
      title: '表单ID',
      width: 200,
    },
    {
      field: 'tableName',
      title: '表单名称',
      minWidth: 120,
    },
    {
      field: 'tableComment',
      title: '表单备注',
      minWidth: 150,
    },
    {
      field: 'tableType',
      title: '表单类型',
      width: 100,
      cellRender: {
        name: 'CellDict',
        props: { type: DICT_TYPE.INFRA_ONLINE_TABLE_TYPE },
      },
    },
    {
      field: 'privateKeyType',
      title: '主键类型',
      width: 100,
      slots: { default: 'privateKeyType' },
    },
    {
      field: 'datasourceId',
      title: '数据源',
      minWidth: 120,
      slots: { default: 'datasource' },
    },
    {
      field: 'tenant',
      title: '多租户',
      width: 80,
      slots: { default: 'tenant' },
    },
    {
      field: 'page',
      title: '分页',
      width: 80,
      slots: { default: 'page' },
    },
    {
      field: 'version',
      title: '版本号',
      width: 80,
      slots: { default: 'version' },
    },
    {
      field: 'status',
      title: '状态',
      width: 80,
      cellRender: {
        name: 'CellDict',
        props: { type: DICT_TYPE.COMMON_STATUS },
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      width: 180,
      formatter: 'formatDateTime',
    },
    {
      title: '操作',
      width: 320,
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ];
}

/** 搜索表单字段 */
export function useSearchFormSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'tableName',
      label: '表单名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入表单名称',
        allowClear: true,
      },
    },
    {
      fieldName: 'tableComment',
      label: '表单备注',
      component: 'Input',
      componentProps: {
        placeholder: '请输入表单备注',
        allowClear: true,
      },
    },
    {
      fieldName: 'tableType',
      label: '表单类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择表单类型',
        allowClear: true,
        options: [
          { label: '单表', value: 1 },
          { label: '主子表', value: 2 },
          { label: '树表', value: 3 },
        ],
        style: {
          width: '120px',
        },
      },
    },
    {
      fieldName: 'datasourceId',
      label: '数据源',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择数据源',
        allowClear: true,
        style: {
          width: '200px',
        },
        api: getDataSourceConfigList,
        labelField: 'name',
        valueField: 'id',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '表单状态',
        allowClear: true,
        options: [
          { label: '开启', value: 0 },
          { label: '关闭', value: 1 },
        ],
        style: {
          width: '100px',
        },
      },
    },
  ];
}

/** 表单字段 - 基本信息 */
export function useFormBaseSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'tableId',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
    {
      fieldName: 'tableName',
      label: '表单名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入表单名称(英文名)',
      },
      rules: 'required',
    },
    {
      fieldName: 'tableComment',
      label: '表单备注',
      component: 'Input',
      componentProps: {
        placeholder: '请输入表单备注',
      },
      rules: 'required',
    },
    {
      fieldName: 'tableType',
      label: '表单类型',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '单表', value: 1 },
          { label: '主子表', value: 2 },
          { label: '树表', value: 3 },
        ],
      },
      defaultValue: 1,
      rules: 'required',
    },
    {
      fieldName: 'privateKeyType',
      label: '主键类型',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '自增', value: 1 },
          { label: 'UUID', value: 2 },
          { label: '雪花算法', value: 3 },
        ],
      },
      defaultValue: 1,
      rules: 'required',
    },
    {
      fieldName: 'datasourceId',
      label: '数据源',
      component: 'Select',
      componentProps: {
        placeholder: '请选择数据源',
        style: {
          width: '300px',
        },
      },
      rules: 'required',
    },
    {
      fieldName: 'tenant',
      label: '多租户',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      defaultValue: 0,
      rules: 'required',
    },
    {
      fieldName: 'page',
      label: '分页查询',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      defaultValue: 1,
      rules: 'required',
    },
    {
      fieldName: 'checkbox',
      label: '显示复选框',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      defaultValue: 0,
    },
    {
      fieldName: 'scrollBar',
      label: '显示滚动条',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      defaultValue: 0,
    },
    {
      fieldName: 'tableFormStyle',
      label: '表单风格',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '默认', value: 0 },
          { label: '卡片', value: 1 },
        ],
      },
      defaultValue: 0,
      rules: 'required',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '开启', value: 0 },
          { label: '关闭', value: 1 },
        ],
      },
      defaultValue: 0,
      rules: 'required',
    },
    {
      fieldName: 'sqlContent',
      label: 'SQL内容',
      component: 'Input',
      dependencies: {
        triggerFields: [''],
        show: () => false,
      },
    },
  ];
}

/** 字段类型选项 */
export const columnTypeOptions = [
  { label: 'VARCHAR', value: 'VARCHAR' },
  { label: 'INT', value: 'INT' },
  { label: 'BIGINT', value: 'BIGINT' },
  { label: 'DECIMAL', value: 'DECIMAL' },
  { label: 'TEXT', value: 'TEXT' },
  { label: 'DATETIME', value: 'DATETIME' },
  { label: 'DATE', value: 'DATE' },
  { label: 'TIME', value: 'TIME' },
  { label: 'TINYINT', value: 'TINYINT' },
  { label: 'SMALLINT', value: 'SMALLINT' },
  { label: 'FLOAT', value: 'FLOAT' },
  { label: 'DOUBLE', value: 'DOUBLE' },
];

/** 组件类型选项 */
export const componentTypeOptions = [
  { label: '输入框', value: 'input' },
  { label: '文本域', value: 'textarea' },
  { label: '数字输入框', value: 'number' },
  { label: '下拉选择', value: 'select' },
  { label: '单选框', value: 'radio' },
  { label: '复选框', value: 'checkbox' },
  { label: '日期选择', value: 'date' },
  { label: '时间选择', value: 'time' },
  { label: '日期时间选择', value: 'datetime' },
  { label: '文件上传', value: 'upload' },
  { label: '富文本编辑器', value: 'editor' },
  { label: '开关', value: 'switch' },
];
