<template>
  <el-dialog
    v-model="dialogVisible"
    title="版本对比"
    width="95%"
    :close-on-click-modal="false"
    top="2vh"
  >
    <div v-if="oldVersion && currentVersion" class="compare-container">
      <!-- 版本信息对比 -->
      <el-card shadow="never" class="compare-header">
        <div class="version-compare-info">
          <div class="version-item old-version">
            <el-tag type="warning" size="large">
              v{{ oldVersion.version || 1 }} (历史版本)
            </el-tag>
            <div class="version-meta">
              <span>创建时间：{{ formatTime(oldVersion.createTime) }}</span>
              <span>创建者：{{ oldVersion.creator || '系统' }}</span>
            </div>
          </div>
          
          <div class="vs-divider">
            <Icon icon="ep:right" size="24" />
          </div>
          
          <div class="version-item current-version">
            <el-tag type="success" size="large">
              v{{ currentVersion.version || 1 }} (当前版本)
            </el-tag>
            <div class="version-meta">
              <span>创建时间：{{ formatTime(currentVersion.createTime) }}</span>
              <span>创建者：{{ currentVersion.creator || '系统' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 基本信息对比 -->
      <el-card shadow="never" class="mt-16px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:info-filled" class="mr-5px" />
            基本信息对比
          </div>
        </template>
        
        <div class="compare-table">
          <el-table :data="basicInfoCompareData" border>
            <el-table-column prop="field" label="字段" width="150" align="center" />
            <el-table-column label="历史版本" align="center">
              <template #default="scope">
                <div :class="{ 'diff-value': scope.row.isDifferent }">
                  {{ scope.row.oldValue }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="当前版本" align="center">
              <template #default="scope">
                <div :class="{ 'diff-value': scope.row.isDifferent }">
                  {{ scope.row.currentValue }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.isDifferent ? 'warning' : 'success'" size="small">
                  {{ scope.row.isDifferent ? '已变更' : '无变更' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 字段配置对比 -->
      <el-card shadow="never" class="mt-16px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:grid" class="mr-5px" />
            字段配置对比
            <div class="compare-stats">
              <el-tag type="success" size="small">新增: {{ fieldStats.added }}</el-tag>
              <el-tag type="warning" size="small">修改: {{ fieldStats.modified }}</el-tag>
              <el-tag type="danger" size="small">删除: {{ fieldStats.deleted }}</el-tag>
            </div>
          </div>
        </template>
        
        <div class="compare-table">
          <el-table :data="fieldCompareData" border stripe>
            <el-table-column prop="columnName" label="字段名称" width="120" />
            <el-table-column label="状态" width="80" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="历史版本" align="center">
              <template #default="scope">
                <div v-if="scope.row.oldColumn" class="field-info">
                  <div>{{ scope.row.oldColumn.columnComment }}</div>
                  <div class="field-meta">
                    {{ scope.row.oldColumn.columnType }}({{ scope.row.oldColumn.columnLength }})
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </el-table-column>
            <el-table-column label="当前版本" align="center">
              <template #default="scope">
                <div v-if="scope.row.currentColumn" class="field-info">
                  <div>{{ scope.row.currentColumn.columnComment }}</div>
                  <div class="field-meta">
                    {{ scope.row.currentColumn.columnType }}({{ scope.row.currentColumn.columnLength }})
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </el-table-column>
            <el-table-column label="变更详情" min-width="200">
              <template #default="scope">
                <div v-if="scope.row.changes && scope.row.changes.length > 0" class="changes-list">
                  <el-tag
                    v-for="change in scope.row.changes"
                    :key="change"
                    type="info"
                    size="small"
                    class="change-tag"
                  >
                    {{ change }}
                  </el-tag>
                </div>
                <span v-else class="empty-value">无变更</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 页面配置对比 -->
      <el-card shadow="never" class="mt-16px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:setting" class="mr-5px" />
            页面配置对比
            <div class="compare-stats">
              <el-tag type="success" size="small">新增: {{ pageStats.added }}</el-tag>
              <el-tag type="warning" size="small">修改: {{ pageStats.modified }}</el-tag>
              <el-tag type="danger" size="small">删除: {{ pageStats.deleted }}</el-tag>
            </div>
          </div>
        </template>
        
        <div class="compare-table">
          <el-table :data="pageCompareData" border stripe>
            <el-table-column prop="columnName" label="字段名称" width="120" />
            <el-table-column label="状态" width="80" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="历史版本" align="center">
              <template #default="scope">
                <div v-if="scope.row.oldPage" class="page-info">
                  <div>{{ scope.row.oldPage.columnText }}</div>
                  <div class="page-meta">
                    {{ scope.row.oldPage.componentType }} | {{ scope.row.oldPage.queryType }}
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </el-table-column>
            <el-table-column label="当前版本" align="center">
              <template #default="scope">
                <div v-if="scope.row.currentPage" class="page-info">
                  <div>{{ scope.row.currentPage.columnText }}</div>
                  <div class="page-meta">
                    {{ scope.row.currentPage.componentType }} | {{ scope.row.currentPage.queryType }}
                  </div>
                </div>
                <span v-else class="empty-value">-</span>
              </template>
            </el-table-column>
            <el-table-column label="变更详情" min-width="200">
              <template #default="scope">
                <div v-if="scope.row.changes && scope.row.changes.length > 0" class="changes-list">
                  <el-tag
                    v-for="change in scope.row.changes"
                    :key="change"
                    type="info"
                    size="small"
                    class="change-tag"
                  >
                    {{ change }}
                  </el-tag>
                </div>
                <span v-else class="empty-value">无变更</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { formatDate } from '@/utils/formatTime'
import * as OnlineTableApi from '@/api/infra/onlineTable/config'

// 响应式数据
const dialogVisible = ref(false)
const oldVersion = ref<OnlineTableApi.OnlineTableVO>()
const currentVersion = ref<OnlineTableApi.OnlineTableVO>()

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return ''
  return formatDate(new Date(timestamp), 'YYYY-MM-DD HH:mm:ss')
}

// 基本信息对比数据
const basicInfoCompareData = computed(() => {
  if (!oldVersion.value || !currentVersion.value) return []
  
  const fields = [
    { key: 'tableName', label: '表格名称' },
    { key: 'tableComment', label: '表格备注' },
    { key: 'tableType', label: '表单类型' },
    { key: 'privateKeyType', label: '主键类型' },
    { key: 'datasourceId', label: '数据源ID' },
    { key: 'status', label: '同步状态' },
    { key: 'tenant', label: '多租户' },
    { key: 'page', label: '分页查询' },
    { key: 'checkbox', label: '显示复选框' },
    { key: 'scrollBar', label: '显示滚动条' },
    { key: 'tableFormStyle', label: '表单风格' }
  ]
  
  return fields.map(field => {
    const oldValue = oldVersion.value![field.key as keyof OnlineTableApi.OnlineTableVO]
    const currentValue = currentVersion.value![field.key as keyof OnlineTableApi.OnlineTableVO]
    const isDifferent = oldValue !== currentValue
    
    return {
      field: field.label,
      oldValue: String(oldValue || ''),
      currentValue: String(currentValue || ''),
      isDifferent
    }
  })
})

// 字段对比数据
const fieldCompareData = computed(() => {
  if (!oldVersion.value || !currentVersion.value) return []
  
  const oldColumns = oldVersion.value.columns || []
  const currentColumns = currentVersion.value.columns || []
  const compareData: any[] = []
  
  // 获取所有字段名称
  const allColumnNames = new Set([
    ...oldColumns.map(col => col.columnName),
    ...currentColumns.map(col => col.columnName)
  ])
  
  allColumnNames.forEach(columnName => {
    const oldColumn = oldColumns.find(col => col.columnName === columnName)
    const currentColumn = currentColumns.find(col => col.columnName === columnName)
    
    let status = 'unchanged'
    let changes: string[] = []
    
    if (!oldColumn && currentColumn) {
      status = 'added'
    } else if (oldColumn && !currentColumn) {
      status = 'deleted'
    } else if (oldColumn && currentColumn) {
      // 检查变更
      const fields = ['columnComment', 'columnType', 'columnLength', 'nullable', 'privateKey', 'syncDb', 'dictType']
      fields.forEach(field => {
        if (oldColumn[field as keyof typeof oldColumn] !== currentColumn[field as keyof typeof currentColumn]) {
          changes.push(field)
          status = 'modified'
        }
      })
    }
    
    compareData.push({
      columnName,
      oldColumn,
      currentColumn,
      status,
      changes
    })
  })
  
  return compareData
})

// 页面配置对比数据
const pageCompareData = computed(() => {
  if (!oldVersion.value || !currentVersion.value) return []
  
  const oldPages = oldVersion.value.pageConfigs || []
  const currentPages = currentVersion.value.pageConfigs || []
  const compareData: any[] = []
  
  // 获取所有字段名称
  const allColumnNames = new Set([
    ...oldPages.map(page => page.columnName),
    ...currentPages.map(page => page.columnName)
  ])
  
  allColumnNames.forEach(columnName => {
    const oldPage = oldPages.find(page => page.columnName === columnName)
    const currentPage = currentPages.find(page => page.columnName === columnName)
    
    let status = 'unchanged'
    let changes: string[] = []
    
    if (!oldPage && currentPage) {
      status = 'added'
    } else if (oldPage && !currentPage) {
      status = 'deleted'
    } else if (oldPage && currentPage) {
      // 检查变更
      const fields = [
        'columnText', 'componentType', 'queryType', 'validateRule',
        'labelLength', 'componentLength', 'componentDefaultValue',
        'queryFlag', 'formShowFlag', 'listShowFlag', 'readonlyFlag',
        'sortFlag', 'sortType', 'requiredFlag', 'dictType'
      ]
      fields.forEach(field => {
        if (oldPage[field as keyof typeof oldPage] !== currentPage[field as keyof typeof currentPage]) {
          changes.push(field)
          status = 'modified'
        }
      })
    }
    
    compareData.push({
      columnName,
      oldPage,
      currentPage,
      status,
      changes
    })
  })
  
  return compareData
})

// 字段统计
const fieldStats = computed(() => {
  const stats = { added: 0, modified: 0, deleted: 0 }
  fieldCompareData.value.forEach(item => {
    if (item.status === 'added') stats.added++
    else if (item.status === 'modified') stats.modified++
    else if (item.status === 'deleted') stats.deleted++
  })
  return stats
})

// 页面配置统计
const pageStats = computed(() => {
  const stats = { added: 0, modified: 0, deleted: 0 }
  pageCompareData.value.forEach(item => {
    if (item.status === 'added') stats.added++
    else if (item.status === 'modified') stats.modified++
    else if (item.status === 'deleted') stats.deleted++
  })
  return stats
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'added': return 'success'
    case 'modified': return 'warning'
    case 'deleted': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'added': return '新增'
    case 'modified': return '修改'
    case 'deleted': return '删除'
    default: return '无变更'
  }
}

// 打开弹窗
const open = (old: OnlineTableApi.OnlineTableVO, current: OnlineTableApi.OnlineTableVO) => {
  oldVersion.value = old
  currentVersion.value = current
  dialogVisible.value = true
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.compare-container {
  .compare-header {
    .version-compare-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      
      .version-item {
        flex: 1;
        text-align: center;
        
        .version-meta {
          margin-top: 8px;
          font-size: 12px;
          color: #666;
          
          span {
            display: block;
            margin: 2px 0;
          }
        }
      }
      
      .vs-divider {
        margin: 0 20px;
        color: #409eff;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    color: #409eff;
    
    .compare-stats {
      display: flex;
      gap: 8px;
    }
  }
  
  .compare-table {
    .diff-value {
      background-color: #fff3cd;
      padding: 2px 4px;
      border-radius: 4px;
      border: 1px solid #ffeaa7;
    }
    
    .field-info, .page-info {
      .field-meta, .page-meta {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
      }
    }
    
    .empty-value {
      color: #999;
      font-style: italic;
    }
    
    .changes-list {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      
      .change-tag {
        font-size: 11px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: bold;
    }
  }
}
</style>
