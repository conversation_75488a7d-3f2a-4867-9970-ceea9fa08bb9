<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`版本详情 - v${currentVersion?.version || 1}`"
    width="90%"
    :close-on-click-modal="false"
    top="5vh"
  >
    <div v-if="currentVersion" class="detail-container">
      <!-- 基本信息 -->
      <el-card shadow="never" class="info-card">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:info-filled" class="mr-5px" />
            基本信息
          </div>
        </template>
        
        <el-descriptions :column="3" border>
          <el-descriptions-item label="表格名称">
            {{ currentVersion.tableName }}
          </el-descriptions-item>
          <el-descriptions-item label="表格备注">
            {{ currentVersion.tableComment }}
          </el-descriptions-item>
          <el-descriptions-item label="版本号">
            <el-tag type="success" size="large">v{{ currentVersion.version || 1 }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="表单类型">
            <dict-tag :type="DICT_TYPE.ONLINE_TABLE_TYPE" :value="currentVersion.tableType" />
          </el-descriptions-item>
          <el-descriptions-item label="主键类型">
            <dict-tag :type="DICT_TYPE.ONLINE_TABLE_PRIVATE_KEY_TYPE" :value="currentVersion.privateKeyType" />
          </el-descriptions-item>
          <el-descriptions-item label="数据源ID">
            {{ currentVersion.datasourceId }}
          </el-descriptions-item>
          <el-descriptions-item label="同步状态">
            <el-tag :type="currentVersion.status === 1 ? 'success' : 'warning'">
              {{ currentVersion.status === 1 ? '已同步' : '未同步' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="多租户">
            <el-tag :type="currentVersion.tenant === 1 ? 'success' : 'info'">
              {{ currentVersion.tenant === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分页查询">
            <el-tag :type="currentVersion.page === 1 ? 'success' : 'info'">
              {{ currentVersion.page === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="显示复选框">
            <el-tag :type="currentVersion.checkbox === 1 ? 'success' : 'info'">
              {{ currentVersion.checkbox === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="显示滚动条">
            <el-tag :type="currentVersion.scrollBar === 1 ? 'success' : 'info'">
              {{ currentVersion.scrollBar === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="表单风格">
            {{ currentVersion.tableFormStyle || 1 }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(currentVersion.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建者">
            {{ currentVersion.creator || '系统' }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatTime(currentVersion.updateTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 字段配置 -->
      <el-card shadow="never" class="mt-16px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:grid" class="mr-5px" />
            字段配置
            <el-tag type="info" size="small" class="ml-10px">
              共 {{ currentVersion.columns?.length || 0 }} 个字段
            </el-tag>
          </div>
        </template>
        
        <el-table :data="currentVersion.columns || []" border stripe>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="columnName" label="字段名称" min-width="120" />
          <el-table-column prop="columnComment" label="字段备注" min-width="120" />
          <el-table-column prop="columnType" label="字段类型" width="100" align="center" >
             <template #default="scope">
              <dict-tag :type="DICT_TYPE.ONLINE_COLUMN_TYPE" :value="scope.row.columnType" />
            </template>
          </el-table-column>
          <el-table-column prop="columnLength" label="长度" width="80" align="center" />
          <el-table-column label="可为空" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.nullable ? 'success' : 'danger'" size="small">
                {{ scope.row.nullable ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="主键" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.privateKey ? 'danger' : 'info'" size="small">
                {{ scope.row.privateKey ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="同步DB" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.syncDb ? 'success' : 'warning'" size="small">
                {{ scope.row.syncDb ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dictType" label="字典类型" width="120" />
        </el-table>
      </el-card>

      <!-- 页面配置 -->
      <el-card shadow="never" class="mt-16px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:setting" class="mr-5px" />
            页面配置
            <el-tag type="info" size="small" class="ml-10px">
              共 {{ currentVersion.pageConfigs?.length || 0 }} 个配置
            </el-tag>
          </div>
        </template>
        
        <el-table :data="currentVersion.pageConfigs || []" border stripe>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="columnName" label="字段名称" min-width="120" />
          <el-table-column prop="columnText" label="显示文本" min-width="120" />
          <el-table-column prop="componentType" label="控件类型" width="100" align="center" >
             <template #default="scope">
              <dict-tag :type="DICT_TYPE.ONLINE_COMPONENT_TYPE" :value="scope.row.componentType" />
            </template>
          </el-table-column>
          <el-table-column prop="queryType" label="查询类型" width="100" align="center" >
             <template #default="scope">
              <dict-tag :type="DICT_TYPE.ONLINE_QUERY_TYPE" :value="scope.row.queryType" />
            </template>
          </el-table-column>
          <el-table-column label="查询字段" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.queryFlag ? 'success' : 'info'" size="small">
                {{ scope.row.queryFlag ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="表单显示" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.formShowFlag ? 'success' : 'info'" size="small">
                {{ scope.row.formShowFlag ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="列表显示" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.listShowFlag ? 'success' : 'info'" size="small">
                {{ scope.row.listShowFlag ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="只读" width="60" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.readonlyFlag ? 'warning' : 'success'" size="small">
                {{ scope.row.readonlyFlag ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="必填" width="60" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.requiredFlag ? 'danger' : 'info'" size="small">
                {{ scope.row.requiredFlag ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sort" label="排序" width="60" align="center" />
        </el-table>
      </el-card>

      <!-- SQL内容 -->
      <el-card shadow="never" class="mt-16px" v-if="currentVersion.sqlContent">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:document" class="mr-5px" />
            SQL内容
          </div>
        </template>
        
        <el-input
          v-model="currentVersion.sqlContent"
          type="textarea"
          :rows="10"
          readonly
          class="sql-content"
        />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import * as OnlineTableApi from '@/api/infra/onlineTable/config'

// 响应式数据
const dialogVisible = ref(false)
const currentVersion = ref<OnlineTableApi.OnlineTableVO>()

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return ''
  return formatDate(new Date(timestamp), 'YYYY-MM-DD HH:mm:ss')
}

// 打开弹窗
const open = (version: OnlineTableApi.OnlineTableVO) => {
  currentVersion.value = version
  dialogVisible.value = true
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.detail-container {
  .info-card {
    .card-header {
      display: flex;
      align-items: center;
      font-weight: bold;
      color: #409eff;
    }
  }
  
  .sql-content {
    :deep(.el-textarea__inner) {
      font-family: 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: bold;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
