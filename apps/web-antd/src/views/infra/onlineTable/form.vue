<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="90%"
    top="5vh"
    :fullscreen="true"
    append-to-body
    destroy-on-close
  >
    <div class="table-form-container">
      <el-scrollbar height="calc(100vh - 160px)" class="table-form-scrollbar">
        <!-- 基本信息部分 -->
        <el-card shadow="never" class="mb-10px">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="formLoading"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="表格名称" prop="tableName">
                  <el-input v-model="formData.tableName" placeholder="请输入表格名称(英文名)" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="表格备注" prop="tableComment">
                  <el-input v-model="formData.tableComment" placeholder="请输入表格备注" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="表单类型" prop="tableType">
                  <el-select v-model="formData.tableType" placeholder="请选择表单类型" style="width: 100%">
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.ONLINE_TABLE_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="主键类型" prop="privateKeyType">
                  <el-select v-model="formData.privateKeyType" placeholder="请选择主键类型" style="width: 100%">
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.ONLINE_TABLE_PRIVATE_KEY_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数据源" prop="datasourceId">
                  <el-select v-model="formData.datasourceId" placeholder="请选择数据源" style="width: 100%">
                    <el-option
                      v-for="item in dataSourceOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="表单风格" prop="tableFormStyle">
                  <el-select v-model="formData.tableFormStyle" placeholder="请选择表单风格" style="width: 100%">
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.ONLINE_TABLE_FORM_STYLE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="版本号" prop="version">
                  <el-input-number
                    v-model="formData.version"
                    :min="1"
                    :max="999"
                    placeholder="版本号"
                    style="width: 100%"
                    :disabled="!!formData.tableId"
                  />
                  <div class="text-xs text-gray-500 mt-1">
                    {{ formData.tableId ? '编辑时版本号自动递增' : '新建时可指定版本号' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="多租户" prop="tenant">
                  <el-radio-group v-model="formData.tenant">
                    <el-radio :value="0">否</el-radio>
                    <el-radio :value="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="分页查询" prop="page">
                  <el-radio-group v-model="formData.page">
                    <el-radio :value="0">否</el-radio>
                    <el-radio :value="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="显示复选框" prop="checkbox">
                  <el-radio-group v-model="formData.checkbox">
                    <el-radio :value="0">否</el-radio>
                    <el-radio :value="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="显示滚动条" prop="scrollBar">
                  <el-radio-group v-model="formData.scrollBar">
                    <el-radio :value="0">否</el-radio>
                    <el-radio :value="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="建表SQL" prop="sqlContent" class="sql-form-item">
                  <div class="sql-editor-container" :class="{ 'fullscreen-mode': isFullscreen }">
                    <div class="sql-editor-wrapper">
                      <Codemirror
                        v-model="formData.sqlContent"
                        :extensions="cmExtensions"
                        :width="'100%'"
                        :height="isFullscreen ? 'calc(100vh - 100px)' : '300px'"
                        placeholder="请输入建表SQL"
                        class="sql-editor"
                      />
                    </div>
                    <div class="sql-button-container" :class="{ 'fullscreen-buttons': isFullscreen }">
                      <el-button
                        type="primary"
                        v-hasPermi="['infrastructure:online-table:parse-sql']"
                        @click="parseSqlContent"
                        :disabled="!formData.datasourceId && !formData.sqlContent">
                        <Icon icon="ep:magic-stick" class="mr-5px" />解析SQL
                      </el-button>
                    </div>
                    <div class="fullscreen-button-container" :class="{ 'fullscreen-buttons': isFullscreen }">
                      <el-button
                        type="default"
                        size="small"
                        @click="toggleFullscreen">
                        <Icon :icon="isFullscreen ? 'ep:close-bold' : 'ep:full-screen'" />
                      </el-button>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 字段配置和页面配置部分 -->
        <el-card shadow="never" class="config-card">
          <template #header>
            <div class="card-header">
              <el-tabs v-model="activeTab" type="border-card" class="config-tabs">
                <el-tab-pane label="字段配置" name="columns" />
                <el-tab-pane label="页面配置" name="pageConfigs" />
              </el-tabs>
            </div>
          </template>

          <!-- 字段配置内容 -->
          <div v-if="activeTab === 'columns'">
            <el-button type="primary" plain @click="addColumn">
              <Icon icon="ep:plus" class="mr-2px" />新增字段
            </el-button>
            <div class="drag-container">
              <!-- 表头行 -->
              <div class="table-header">
                <el-row :gutter="10" align="middle">
                  <el-col :span="1">
                    <span>排序</span>
                  </el-col>
                  <el-col :span="1">
                    <span>序号</span>
                  </el-col>
                  <el-col :span="3">
                    <span>字段名称</span>
                  </el-col>
                  <el-col :span="3">
                    <span>字段备注</span>
                  </el-col>
                  <el-col :span="2">
                    <span>字段类型</span>
                  </el-col>
                  <el-col :span="2">
                    <span>字段长度</span>
                  </el-col>
                  <el-col :span="2">
                    <span>是否主键</span>
                  </el-col>
                  <el-col :span="2">
                    <span>允许空值</span>
                  </el-col>
                  <el-col :span="2">
                    <span>同步数据库</span>
                  </el-col>
                  <el-col :span="3">
                    <span>字典类型</span>
                  </el-col>
                  <el-col :span="3">
                    <span>操作</span>
                  </el-col>
                </el-row>
              </div>
              <draggable
                v-model="formData.columns"
                :item-key="(item, index) => item.columnName || `column-${index}`"
                handle=".drag-handle"
                ghost-class="sortable-ghost"
                @end="updateColumnsSort"
                :animation="300"
                class="drag-list"
              >
                <template #item="{element, index}">
                  <div class="drag-item">
                    <el-row :gutter="10" class="mb-10px" align="middle">
                      <el-col :span="1">
                        <el-button type="primary" size="small" class="drag-handle">
                          <Icon icon="ep:d-arrow-up-down"/>
                        </el-button>
                      </el-col>
                      <el-col :span="1">
                        <span>{{ index + 1 }}</span>
                      </el-col>
                      <el-col :span="3">
                        <el-input
                          v-model="element.columnName"
                          placeholder="请输入字段名称"
                          @blur="handleColumnNameChange(index, element.columnName)" />
                      </el-col>
                      <el-col :span="3">
                        <el-input
                          v-model="element.columnComment"
                          placeholder="请输入字段备注"
                          @blur="handleColumnCommentChange(index, element.columnComment)" />
                      </el-col>
                      <el-col :span="2">
                        <el-select v-model="element.columnType" placeholder="请选择字段类型" style="width: 100%">
                          <el-option
                            v-for="dict in getStrDictOptions(DICT_TYPE.ONLINE_COLUMN_TYPE) || []"
                            :key="dict.value || dict.label"
                            :label="dict.label + '(' + dict.value + ')'"
                            :value="dict.value"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="2">
                        <el-input-number v-model="element.columnLength" :min="0" style="width: 100%" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.privateKey" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.nullable" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.syncDb" />
                      </el-col>
                      <el-col :span="3">
                        <el-select
                          v-model="element.dictType"
                          placeholder="请选择字典类型"
                          clearable
                          style="width: 100%">
                          <el-option
                            v-for="dict in dictTypeOptions || []"
                            :key="dict.type || dict.name"
                            :label="dict.name"
                            :value="dict.type"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="3">
                        <el-button
                          link
                          type="danger"
                          @click="deleteColumn(index)"
                          :disabled="element.isSystem">
                          {{ element.isSystem ? '系统字段' : '删除' }}
                        </el-button>
                      </el-col>
                    </el-row>
                  </div>
                </template>
              </draggable>
            </div>
          </div>

          <!-- 页面配置内容 -->
          <div v-if="activeTab === 'pageConfigs'">
            <!-- <el-button type="primary" plain @click="addPageConfig">
              <Icon icon="ep:plus" class="mr-2px" />新增页面配置
            </el-button> -->
            <div class="drag-container">
              <!-- 表头行 -->
              <div class="table-header">
                <el-row :gutter="10" align="middle">
                  <el-col :span="1">
                    <span>排序</span>
                  </el-col>
                  <el-col :span="1">
                    <span>序号</span>
                  </el-col>
                  <el-col :span="2">
                    <span>字段名称</span>
                  </el-col>
                  <el-col :span="2">
                    <span>字段文本</span>
                  </el-col>
                  <el-col :span="2">
                    <span>查询条件</span>
                  </el-col>
                  <el-col :span="2">
                    <span>表单显示</span>
                  </el-col>
                  <el-col :span="2">
                    <span>列表显示</span>
                  </el-col>
                  <el-col :span="2">
                    <span>只读</span>
                  </el-col>
                  <el-col :span="2">
                    <span>排序</span>
                  </el-col>
                  <el-col :span="2">
                    <span>查询类型</span>
                  </el-col>
                  <el-col :span="2">
                    <span>控件类型</span>
                  </el-col>
                  <el-col :span="2">
                    <span>必填</span>
                  </el-col>
                  <!-- <el-col :span="2">
                    <span>操作</span>
                  </el-col> -->
                </el-row>
              </div>
              <draggable
                v-model="formData.pageConfigs"
                :item-key="(item, index) => item.columnName || `page-config-${index}`"
                handle=".drag-handle"
                ghost-class="sortable-ghost"
                @end="updatePageConfigsSort"
                :animation="300"
                class="drag-list"
              >
                <template #item="{element, index}">
                  <div class="drag-item">
                    <el-row :gutter="10" class="mb-10px" align="middle">
                      <el-col :span="1">
                        <el-button type="primary" size="small" class="drag-handle">
                          <Icon icon="ep:d-arrow-up-down"/>
                        </el-button>
                      </el-col>
                      <el-col :span="1">
                        <span>{{ index + 1 }}</span>
                      </el-col>
                      <el-col :span="2">
                        <el-input
                          v-model="element.columnName"
                          placeholder="字段名称"
                          :disabled="element.isSystem" />
                      </el-col>
                      <el-col :span="2">
                        <el-input v-model="element.columnText" placeholder="字段文本" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.queryFlag" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.formShowFlag" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.listShowFlag" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.readonlyFlag" />
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.sortFlag" />
                      </el-col>
                      <el-col :span="2">
                        <el-select v-model="element.queryType" placeholder="查询类型" style="width: 100%">
                          <el-option
                            v-for="dict in getIntDictOptions(DICT_TYPE.ONLINE_QUERY_TYPE)"
                            :key="dict.value || dict.label"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="2">
                        <el-select v-model="element.componentType" placeholder="控件类型" style="width: 100%">
                          <el-option
                            v-for="dict in getStrDictOptions(DICT_TYPE.ONLINE_COMPONENT_TYPE)"
                            :key="dict.value || dict.label"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="2">
                        <el-checkbox v-model="element.requiredFlag" />
                      </el-col>
                      <!-- <el-col :span="2">
                        <el-button
                          link
                          type="danger"
                          @click="deletePageConfig(index)"
                          :disabled="element.isSystem">
                          {{ element.isSystem ? '系统字段' : '删除' }}
                        </el-button>
                      </el-col> -->
                    </el-row>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </el-card>
      </el-scrollbar>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {DICT_TYPE, getIntDictOptions, getStrDictOptions} from '@/utils/dict'
import * as OnlineTableApi from '@/api/infra/onlineTable'
import * as DataSourceConfigApi from '@/api/infra/dataSourceConfig'
import * as DictTypeApi from '@/api/system/dict/dict.type'
import { reactive, ref, nextTick, onMounted, onBeforeUnmount, watch } from 'vue'
import draggable from 'vuedraggable'
import { Codemirror } from 'vue-codemirror'
import { sql } from '@codemirror/lang-sql'

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const activeTab = ref('columns')
const formType = ref('') // create or update
const formRef = ref()
const isFullscreen = ref(false)
const emit = defineEmits(['success'])

// 数据源选项
const dataSourceOptions = ref<DataSourceConfigApi.DataSourceConfigVO[]>([])

// 字典类型选项
const dictTypeOptions = ref<{type: string, name: string}[]>([])

const formData = reactive<OnlineTableApi.OnlineTableSaveDTO>({
  tableName: '',
  tableComment: '',
  sqlContent: '',
  tableType: 1,
  privateKeyType: 1,
  datasourceId: undefined as unknown as number,
  status: 0,
  tenant: 0,
  page: 1,
  checkbox: 0,
  scrollBar: 0,
  tableFormStyle: 0,
  version: 1,
  columns: [],
  pageConfigs: []
})

const formRules = reactive({
  tableName: [{ required: true, message: '表格名称不能为空', trigger: 'blur' }],
  tableComment: [{ required: true, message: '表格备注不能为空', trigger: 'blur' }],
  tableType: [{ required: true, message: '表单类型不能为空', trigger: 'change' }],
  privateKeyType: [{ required: true, message: '主键类型不能为空', trigger: 'change' }],
  datasourceId: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
  tableFormStyle: [{ required: true, message: '表单风格不能为空', trigger: 'change' }]
})

// CodeMirror 扩展
const cmExtensions = [sql()]

// 系统默认字段配置（不可删除）
const SYSTEM_COLUMNS = [
  {
    columnName: 'deleted',
    columnComment: '是否删除',
    columnType: 'bit',
    columnLength: 1,
    nullable: false,
    privateKey: false,
    syncDb: false,
    dictType: '',
    isSystem: true // 标记为系统字段
  },
  {
    columnName: 'creator',
    columnComment: '创建者',
    columnType: 'varchar',
    columnLength: 64,
    nullable: false,
    privateKey: false,
    syncDb: false,
    dictType: '',
    isSystem: true
  },
  {
    columnName: 'create_time',
    columnComment: '创建时间（时间戳）',
    columnType: 'bigint',
    columnLength: 20,
    nullable: false,
    privateKey: false,
    syncDb: false,
    dictType: '',
    isSystem: true
  },
  {
    columnName: 'updater',
    columnComment: '更新者',
    columnType: 'varchar',
    columnLength: 64,
    nullable: false,
    privateKey: false,
    syncDb: false,
    dictType: '',
    isSystem: true
  },
  {
    columnName: 'update_time',
    columnComment: '更新时间（时间戳）',
    columnType: 'bigint',
    columnLength: 20,
    nullable: false,
    privateKey: false,
    syncDb: false,
    dictType: '',
    isSystem: true
  }
]

/** 获取数据源列表 */
const getDataSourceList = async () => {
  try {
    const res = await DataSourceConfigApi.getDataSourceConfigList()
    dataSourceOptions.value = res || []
  } catch (error) {
    console.error('获取数据源列表失败', error)
  }
}

/** 获取字典类型列表 */
const getDictTypeList = async () => {
  try {
    const res = await DictTypeApi.getSimpleDictTypeList()
    dictTypeOptions.value = res || []
  } catch (error) {
    console.error('获取字典类型列表失败', error)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formType.value = type
  resetForm()

  // 获取数据源列表和字典类型列表
  await Promise.all([getDataSourceList(), getDictTypeList()])

  // 修改
  if (type === 'update' && id) {
    dialogTitle.value = '修改表单'
    formLoading.value = true
    try {
      const res = await OnlineTableApi.getOnlineTable(id)
      Object.assign(formData, res)
    } finally {
      formLoading.value = false
    }
  } else {
    // 添加
    dialogTitle.value = '新增表单'
    // 新增时初始化系统字段
    initSystemColumns()
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await OnlineTableApi.createOnlineTable(formData)
      ElMessage.success('新增成功')
    } else {
      await OnlineTableApi.updateOnlineTable(formData)
      ElMessage.success('修改成功')
    }
    dialogVisible.value = false
    // 通知父组件刷新
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.tableId = undefined
  formData.tableName = ''
  formData.tableComment = ''
  formData.sqlContent = ''
  formData.tableType = 1
  formData.privateKeyType = 1
  formData.datasourceId = undefined as unknown as number
  formData.status = 0
  formData.tenant = 0
  formData.page = 1
  formData.checkbox = 0
  formData.scrollBar = 0
  formData.tableFormStyle = 0
  formData.version = 1
  formData.columns = []
  formData.pageConfigs = []
  activeTab.value = 'columns'

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 初始化系统字段 */
const initSystemColumns = () => {
  // 添加系统默认字段
  formData.columns = [...SYSTEM_COLUMNS.map(col => ({ ...col }))]

  // 同步生成页面配置
  syncPageConfigsFromColumns()
}

/** 从字段配置同步页面配置 */
const syncPageConfigsFromColumns = () => {
  formData.pageConfigs = formData.columns.map((col: any, index: number) => ({
    columnName: col.columnName,
    columnText: col.columnComment || col.columnName,
    queryFlag: !col.privateKey && !col.isSystem, // 主键和系统字段不作为查询条件
    formShowFlag: !col.privateKey && !col.isSystem, // 主键和系统字段不在表单中显示
    listShowFlag: !col.isSystem, // 系统字段不在列表中显示（除了特殊需要）
    readonlyFlag: col.isSystem || false, // 系统字段设为只读
    sortFlag: false,
    sortType: 'no',
    requiredFlag: !col.nullable && !col.isSystem,
    componentType: getDefaultComponentType(col.columnType),
    queryType: getDefaultQueryType(col.columnType),
    validateRule: col.nullable ? '' : 'required',
    labelLength: 100,
    componentLength: 200,
    componentDefaultValue: '',
    sort: index + 1,
    dictType: col.dictType || '',
    isSystem: col.isSystem || false // 标记为系统字段
  }))
  console.log('formData.pageConfigs', formData.pageConfigs)
}

/** 解析SQL */
const parseSqlContent = async () => {
  if (!formData.datasourceId && !formData.sqlContent) {
    ElMessage.warning('请先选择数据源并输入SQL语句')
    return
  }

  formLoading.value = true
  try {
    const res = await OnlineTableApi.parseSqlToTableStructure({
      datasourceId: formData.datasourceId,
      sqlContent: formData.sqlContent
    })

    // 合并解析结果到表单
    if (res && res.columns && res.columns.length > 0) {
      // 更新表单基本信息
      if (res.tableName) {
        formData.tableName = res.tableName
      }

      // 更新字段配置
      formData.columns = res.columns.map((col: any) => ({
        columnName: col.columnName,
        columnComment: col.columnComment || col.columnName,
        columnLength: col.columnLength || 255,
        columnType: col.columnType || 'varchar',
        privateKey: col.privateKey || false,
        nullable: col.nullable !== false,
        syncDb: col.syncDb !== false,
        dictType: col.dictType || ''
      }))

      // 根据字段自动生成默认页面配置
      formData.pageConfigs = formData.columns.map((col: any, index: number) => ({
        columnName: col.columnName,
        columnText: col.columnComment || col.columnName,
        queryFlag: !col.privateKey, // 主键不作为查询条件
        formShowFlag: !col.privateKey, // 主键不在表单中显示
        listShowFlag: true, // 所有字段都在列表中显示
        readonlyFlag: col.privateKey, // 主键只读
        sortFlag: false,
        sortType: 'no',
        queryType: col.columnType === 'varchar' ? 'like' : 'eq', // 字符串类型默认模糊查询
        labelLength: 4,
        componentType: getDefaultComponentType(col.columnType),
        componentLength: undefined,
        componentDefaultValue: '',
        validateRule: '',
        requiredFlag: !col.nullable && !col.privateKey,
        sort: index + 1,
        dictType: col.dictType || ''
      }))

      ElMessage.success('SQL解析成功')
    } else {
      ElMessage.warning('未解析到字段信息')
    }
  } catch (error) {
    console.error('解析SQL失败', error)
    // ElMessage.error('解析SQL失败')
  } finally {
    formLoading.value = false
  }
}

/** 根据字段类型获取默认控件类型 */
const getDefaultComponentType = (columnType: string): string => {
  const type = columnType.toLowerCase()

  // 数字类型
  if (type.includes('int') || type.includes('decimal') || type.includes('float') ||
      type.includes('double') || type.includes('number')) {
    return 'input-number'
  }

  // 日期时间类型
  if (type.includes('datetime') || type.includes('timestamp')) {
    return 'datetime-picker'
  } else if (type.includes('date')) {
    return 'date-picker'
  } else if (type.includes('time')) {
    return 'time-picker'
  }

  // 文本类型
  if (type.includes('text') || type.includes('longtext') || type.includes('mediumtext')) {
    return 'textarea'
  }

  // 布尔类型
  if (type.includes('tinyint') && type.includes('(1)')) {
    return 'switch'
  }

  // 枚举类型
  if (type.includes('enum')) {
    return 'select'
  }

  // 默认输入框
  return 'input'
}

/** 根据字段类型获取默认查询类型 */
const getDefaultQueryType = (columnType: string): string => {
  const type = columnType.toLowerCase()
  // 字符串类型默认模糊查询
  if (type.includes('varchar') || type.includes('char') || type.includes('text')) {
    return 'like'
  }

  // 数字类型和日期类型默认精确查询
  if (type.includes('int') || type.includes('decimal') || type.includes('float') ||
      type.includes('double') || type.includes('date') || type.includes('time')) {
    return 'eq'
  }

  // 默认精确查询
  return 'eq'
}

/** 切换全屏模式 */
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  // 全屏模式下禁止滚动
  if (isFullscreen.value) {
    document.body.style.overflow = 'hidden'
    // 添加ESC键监听
    document.addEventListener('keydown', handleEscKey)
  } else {
    document.body.style.overflow = ''
    // 移除ESC键监听
    document.removeEventListener('keydown', handleEscKey)
  }
}

/** 处理ESC键退出全屏 */
const handleEscKey = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isFullscreen.value) {
    toggleFullscreen()
  }
}

/** 新增字段 */
const addColumn = () => {
  // 生成唯一的字段名称
  const newFieldIndex = formData.columns.length + 1
  const defaultColumnName = `field_${newFieldIndex}`

  const newColumn = {
    columnName: defaultColumnName,
    columnComment: `字段${newFieldIndex}`,
    columnLength: 255,
    columnType: 'varchar',
    privateKey: false,
    nullable: true,
    syncDb: false,
    dictType: '',
    isSystem: false
  }

  formData.columns.push(newColumn)

  // 同步添加页面配置
  const newPageConfig = {
    columnName: defaultColumnName,
    columnText: `字段${newFieldIndex}`,
    queryFlag: false,
    formShowFlag: true,
    listShowFlag: true,
    readonlyFlag: false,
    sortFlag: false,
    sortType: 'no',
    queryType: 'eq',
    labelLength: 100,
    componentType: 'input',
    componentLength: 200,
    componentDefaultValue: '',
    validateRule: '',
    requiredFlag: false,
    sort: formData.pageConfigs.length + 1,
    dictType: '',
    isSystem: false
  }

  formData.pageConfigs.push(newPageConfig)
}

/** 删除字段 */
const deleteColumn = (index: number) => {
  const column = formData.columns[index]

  // 系统字段不能删除
  if (column.isSystem) {
    ElMessage.warning('系统字段不能删除')
    return
  }

  const columnName = column.columnName

  // 删除字段配置
  formData.columns.splice(index, 1)

  // 同步删除对应的页面配置
  const pageConfigIndex = formData.pageConfigs.findIndex(config => config.columnName === columnName)
  if (pageConfigIndex !== -1) {
    formData.pageConfigs.splice(pageConfigIndex, 1)
  }

  // 重新排序
  updateColumnsSort()
  updatePageConfigsSort()
}

/** 更新字段排序 */
const updateColumnsSort = () => {
  // 拖拽排序后的处理
}

/** 新增页面配置 */
const addPageConfig = () => {
  formData.pageConfigs.push({
    columnName: '',
    columnText: '',
    queryFlag: false,
    formShowFlag: true,
    listShowFlag: true,
    readonlyFlag: false,
    sortFlag: false,
    sortType: 'no',
    queryType: 'eq',
    labelLength: 4,
    componentType: 'input',
    componentLength: undefined,
    componentDefaultValue: '',
    validateRule: '',
    requiredFlag: false,
    sort: formData.pageConfigs.length + 1,
    dictType: ''
  })
}

/** 删除页面配置 */
const deletePageConfig = (index: number) => {
  const pageConfig = formData.pageConfigs[index]

  // 系统字段对应的页面配置不能删除
  if (pageConfig.isSystem) {
    ElMessage.warning('系统字段对应的页面配置不能删除')
    return
  }

  formData.pageConfigs.splice(index, 1)

  // 重新排序
  updatePageConfigsSort()
}

/** 更新页面配置排序 */
const updatePageConfigsSort = () => {
  // 拖拽排序后的处理 - 使用响应式安全的方式
  formData.pageConfigs = formData.pageConfigs.map((item, index) => ({
    ...item,
    sort: index + 1
  }))
}

/** 同步字段名称到页面配置 */
const syncColumnNameToPageConfig = (columnIndex: number, oldColumnName: string, newColumnName: string) => {
  // 查找对应的页面配置
  const pageConfigIndex = formData.pageConfigs.findIndex(config => config.columnName === oldColumnName)
  if (pageConfigIndex !== -1) {
    // 使用响应式安全的方式更新
    formData.pageConfigs = formData.pageConfigs.map((config, index) =>
      index === pageConfigIndex
        ? { ...config, columnName: newColumnName }
        : config
    )
  }
}

/** 处理字段名称变化 */
const handleColumnNameChange = (columnIndex: number, newColumnName: string) => {
  // 使用新的同步方法
  const column = formData.columns[columnIndex]
  if (column) {
    syncPageConfigFromColumn(columnIndex, newColumnName, column.columnComment || '')
  }
}

/** 处理字段备注变化 */
const handleColumnCommentChange = (columnIndex: number, newColumnComment: string) => {
  // 使用新的同步方法
  const column = formData.columns[columnIndex]
  if (column) {
    syncPageConfigFromColumn(columnIndex, column.columnName || '', newColumnComment)
  }
}

// 使用 watchEffect 替代 watch，避免响应式冲突
let isManualSync = false

// 手动同步方法，避免在watch中操作响应式数据
const syncPageConfigFromColumn = (columnIndex: number, columnName: string, columnComment: string) => {
  if (isManualSync) return

  isManualSync = true

  // 使用 requestAnimationFrame 而不是 setTimeout，确保在下一个渲染帧执行
  requestAnimationFrame(() => {
    if (formData.pageConfigs.length > columnIndex) {
      const pageConfig = formData.pageConfigs[columnIndex]
      if (pageConfig) {
        let needUpdate = false
        const updates: any = {}

        // 检查字段名称是否需要更新
        if (pageConfig.columnName !== columnName) {
          updates.columnName = columnName
          needUpdate = true
          console.log(`字段名称同步: 索引${columnIndex} -> ${columnName}`)
        }

        // 检查字段备注是否需要更新（只在页面配置的columnText为空时同步）
        if (!pageConfig.columnText && pageConfig.columnText !== columnComment) {
          updates.columnText = columnComment
          needUpdate = true
          console.log(`字段备注同步: 索引${columnIndex} -> ${columnComment}`)
        }

        // 只有在需要更新时才创建新对象
        if (needUpdate) {
          // 使用 Vue 的响应式安全方式更新整个数组
          const newPageConfigs = [...formData.pageConfigs]
          newPageConfigs[columnIndex] = { ...pageConfig, ...updates }
          formData.pageConfigs = newPageConfigs
        }
      }
    }

    isManualSync = false
  })
}

// 监听字段配置数量变化
watch(() => formData.columns.length, (newLength, oldLength) => {
  if (newLength !== oldLength) {
    console.log(`字段数量变化: ${oldLength} -> ${newLength}`)
  }
})

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleEscKey)
  // 恢复body滚动
  document.body.style.overflow = ''
})

defineExpose({ open })
</script>

<style lang="scss" scoped>
.table-form-container {
  height: 100%;
}

.table-form-scrollbar {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-card {
  .config-tabs {
    margin: 0;
    border: none;

    :deep(.el-tabs__header) {
      margin: 0;
    }
  }
}

.sql-form-item {
  height: auto;
  min-height: 300px;
}

.sql-form-item :deep(.el-form-item__content) {
  height: auto;
  min-height: 300px;
}

.sql-editor-container {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  min-height: 300px;
  transition: all 0.3s;
}

.sql-editor-wrapper {
  width: 100%;
  height: 300px;
  position: relative;
  transition: all 0.3s;
}

.sql-editor {
  height: 300px !important;
  min-height: 200px !important;
  width: 100%;
  transition: all 0.3s;
}

.sql-editor :deep(.cm-editor) {
  height: 300px !important;
  min-height: 200px !important;
  width: 100%;
  transition: all 0.3s;
}

.sql-button-container {
  position: absolute;
  right: 100px;
  top: 10px;
  z-index: 20;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.fullscreen-button-container {
  position: absolute;
  right: 50px;
  top: 10px;
  z-index: 20;
  transition: all 0.3s;
}

/* 全屏模式样式 */
.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2000;
  background-color: #fff;
  padding: 20px;
  box-sizing: border-box;
}

.fullscreen-mode .sql-editor-wrapper {
  height: calc(100vh - 100px);
}

.fullscreen-mode .sql-editor,
.fullscreen-mode .sql-editor :deep(.cm-editor) {
  height: calc(100vh - 100px) !important;
}

.fullscreen-buttons {
  top: 20px;
}

.drag-container {
  margin-top: 10px;
}

.table-header {
  background-color: #f5f7fa;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;

  span {
    font-weight: bold;
    color: #606266;
    font-size: 12px;
  }
}

.drag-list {
  min-height: 50px;
}

.drag-item {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.sortable-ghost {
  opacity: 0.5;
  background: #f0f9ff;
}

.drag-handle {
  cursor: move;

  &:hover {
    background-color: #409eff;
  }
}

.dialog-footer {
  text-align: right;
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>
