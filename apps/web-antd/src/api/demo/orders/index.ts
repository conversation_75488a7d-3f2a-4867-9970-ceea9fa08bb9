/* eslint-disable @typescript-eslint/no-unused-vars */
import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export namespace OrdersApi {
  /** 订单信息 */
  export interface OrdersVO {
    id?: number;
    orderNo: string;
    userId: number;
    status: number;
    totalAmount: number;
    payAmount?: number;
    payType?: number;
    payTime?: number;
    createTime?: number;
    updateTime?: number;
    remark?: string;
  }

  /** 订单分页查询 DTO */
  export interface OrdersQueryDTO extends PageParam {
    orderNo?: string;
    userId?: number;
    status?: number;
    payType?: number;
    minTotalAmount?: number;
    maxTotalAmount?: number;
    createTime?: number[];
    payTime?: number[];
  }

  /** 订单新增 DTO */
  export interface OrdersAddDTO {
    orderNo: string;
    userId: number;
    totalAmount: number;
    remark?: string;
  }

  /** 订单更新 DTO */
  export interface OrdersUpdateDTO {
    id: number;
    status?: number;
    payAmount?: number;
    payType?: number;
    payTime?: number;
    remark?: string;
  }

  /** 订单导出 DTO */
  export interface OrdersExportDTO extends PageParam {
    orderNo?: string;
    userId?: number;
    status?: number;
    payType?: number;
    minTotalAmount?: number;
    maxTotalAmount?: number;
    createTime?: number[];
    payTime?: number[];
  }
}

/** 获得订单分页 */
export function getOrdersPage(params: OrdersApi.OrdersQueryDTO) {
  return requestClient.post<PageResult<OrdersApi.OrdersVO>>(
    '/demo/orders/page',
    params,
  );
}

/** 获得订单 */
export function getOrders(id: number) {
  return requestClient.get<OrdersApi.OrdersVO>(`/demo/orders/get?id=${id}`);
}

/** 创建订单 */
export function createOrders(data: OrdersApi.OrdersAddDTO) {
  return requestClient.post<number>('/demo/orders/create', data);
}

/** 更新订单 */
export function updateOrders(data: OrdersApi.OrdersUpdateDTO) {
  return requestClient.put<boolean>('/demo/orders/update', data);
}

/** 删除订单 */
export function deleteOrders(id: number) {
  return requestClient.delete<boolean>(`/demo/orders/delete?id=${id}`);
}

/** 支付订单 */
export function payOrders(id: number, payAmount: number, payType: number) {
  return requestClient.post<boolean>('/demo/orders/pay', null, {
    params: { id, payAmount, payType },
  });
}

/** 取消订单 */
export function cancelOrders(id: number) {
  return requestClient.post<boolean>('/demo/orders/cancel', null, {
    params: { id },
  });
}

/** 导出订单Excel */
export function exportOrders(params: OrdersApi.OrdersExportDTO) {
  return requestClient.download('/demo/orders/export', { params });
}

/** 获得导入订单模板 */
export function getOrdersImportTemplate() {
  return requestClient.download('/demo/orders/get-import-template');
}

/** 导入订单 */
export function importOrders(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<string>('/demo/orders/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
