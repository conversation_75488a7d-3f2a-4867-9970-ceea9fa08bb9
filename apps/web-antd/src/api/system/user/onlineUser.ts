import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export interface OnlineUserPageReqVO extends PageParam {
  username?: string;
  clientIp?: string;
}

export interface OnlineUserRespVO {
  userId: number;
  username: string;
  clientIp: string;
  loginTime: number;
  onlineTime: number;
}

// 获得在线用户分页
export function getOnlineUserPage(params: OnlineUserPageReqVO) {
  return requestClient.get<PageResult<OnlineUserRespVO>>(
    '/system/online-user/page',
    { params },
  );
}

// 踢用户下线
export function kickUser(userId: number) {
  return requestClient.post(`/system/online-user/kick?userId=${userId}`);
}

// 封禁用户
export function disableUser(userId: number, disableTime: number) {
  return requestClient.post(
    `/system/online-user/disable?userId=${userId}&disableTime=${disableTime}`,
  );
}
