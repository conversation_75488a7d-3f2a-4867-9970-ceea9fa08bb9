import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export namespace SystemLanguageTranslateApi {
  /** 翻译信息 */
  export interface Translate {
    id?: number;
    langKey: string;
    langTranslate: string;
    createTime?: Date;
  }

  /** 查询翻译列表的参数 */
  export interface TranslatePageReqVO extends PageParam {
    langKey?: string;
    langTranslate?: string;
  }
}

/** 获取翻译列表（全部） */
export function getTranslateAll() {
  return requestClient.get<SystemLanguageTranslateApi.Translate[]>('/system/language-translate/select-all');
}

/** 获取翻译列表（分页） */
export function getTranslatePage(params: SystemLanguageTranslateApi.TranslatePageReqVO) {
  return requestClient.post<PageResult<SystemLanguageTranslateApi.Translate>>('/system/language-translate/page', params);
}

/** 新增翻译 */
export function addTranslate(data: SystemLanguageTranslateApi.Translate) {
  return requestClient.post('/system/language-translate/add', data);
}

/** 更新翻译 */
export function updateTranslate(data: SystemLanguageTranslateApi.Translate) {
  return requestClient.post('/system/language-translate/update', data);
}

/** 删除翻译 */
export function deleteTranslate(ids: number[]) {
  return requestClient.post('/system/language-translate/delete', ids);
}

/** 导出Excel */
export function exportTranslateExcel(params: SystemLanguageTranslateApi.TranslatePageReqVO) {
  return requestClient.download('/system/language-translate/export-excel', { params });
}

/** 导入Excel */
export function importTranslateExcel(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post('/system/language-translate/import-excel', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
