import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export namespace SystemLanguageApi {
  /** 语言信息 */
  export interface Language {
    id?: number;
    langCode: string;
    langName: string;
    enableFlag: number;
    defaultFlag: number;
    createTime?: Date;
  }

  /** 查询语言列表的参数 */
  export interface LanguagePageReqVO extends PageParam {
    langCode?: string;
    langName?: string;
    enableFlag?: number;
  }
}

/** 获取语言列表（全部） */
export function getLanguageAll() {
  return requestClient.get<SystemLanguageApi.Language[]>('/system/language/select-all');
}

/** 获取语言列表（分页） */
export function getLanguagePage(params: SystemLanguageApi.LanguagePageReqVO) {
  return requestClient.post<PageResult<SystemLanguageApi.Language>>('/system/language/page', params);
}

/** 新增语言 */
export function addLanguage(data: SystemLanguageApi.Language) {
  return requestClient.post('/system/language/add', data);
}

/** 更新语言 */
export function updateLanguage(data: SystemLanguageApi.Language) {
  return requestClient.post('/system/language/update', data);
}

/** 删除语言 */
export function deleteLanguage(langCodes: string[]) {
  return requestClient.post('/system/language/delete', langCodes);
}
