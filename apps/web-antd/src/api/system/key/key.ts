import { requestClient } from '#/api/request';

// ========== 秘钥管理接口 ==========

// 分页查询参数
export interface KeyPageReqVO {
  pageNo: number;
  pageSize: number;
  appId?: string;
  appSecretKey?: string;
  remark?: string;
  createTime?: number[];
}

// 秘钥信息响应VO
export interface KeyRespVO {
  id: number;
  appId: string;
  appSecretKey: string;
  remark?: string;
  creator?: string;
  createTime: number;
  updater?: string;
  updateTime?: number;
}

// 分页结果
export interface PageResultKeyRespVO {
  list: KeyRespVO[];
  total: number;
}

// 创建/修改秘钥请求DTO
export interface KeySaveReqDTO {
  id?: number;
  appId: string;
  appSecretKey: string;
  remark?: string;
}

// 修改秘钥请求DTO
export interface KeyUpdateReqDTO {
  id: number;
  remark?: string;
}

// 生成秘钥响应VO
export interface KeyGenerateRespVO {
  appId: string;
  appSecretKey: string;
}

// ========== API接口方法 ==========

// 获取秘钥分页
export const getKeyPage = (params: KeyPageReqVO) => {
  return requestClient.get<PageResultKeyRespVO>({
    url: '/system/key/page',
    params,
  });
};

// 获得秘钥详情
export const getKey = (id: number) => {
  return requestClient.get<KeyRespVO>({
    url: '/system/key/get',
    params: { id },
  });
};

// 创建秘钥
export const createKey = (data: KeySaveReqDTO) => {
  return requestClient.post<number>({ url: '/system/key/create', data });
};

// 修改秘钥备注
export const updateKey = (data: KeyUpdateReqDTO) => {
  return requestClient.put<boolean>({ url: '/system/key/update', data });
};

// 删除秘钥
export const deleteKey = (id: number) => {
  return requestClient.delete<boolean>({
    url: '/system/key/delete',
    params: { id },
  });
};

// 生成随机秘钥
export const generateKey = () => {
  return requestClient.get<KeyGenerateRespVO>({ url: '/system/key/generate' });
};
