import type { PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

export interface OnlineReportVO {
  reportId: string;
  name: string;
  code: string;
  sqlContent: string;
  datasourceId: string;
  status: number;
  remark: string;
  creator: string;
  createTime: number;
  updater: string;
  updateTime: number;
  params: OnlineReportParamVO[];
  columns: OnlineReportColumnVO[];
  tenant?: number;
  page?: number;
}

export interface OnlineReportParamVO {
  paramId: string;
  reportId: string;
  paramName: string;
  paramText: string;
  defaultValue: string;
  sort: number;
  paramType?: string;
  dictType: string;
}

export interface OnlineReportColumnVO {
  columnId: string;
  reportId: string;
  columnName: string;
  columnText: string;
  columnLength: number;
  paramType: string;
  showFlag: boolean;
  sort: number;
  dictType: string;
}

export interface OnlineReportQueryDTO {
  pageNo: number;
  pageSize: number;
  name?: string;
  code?: string;
  datasourceId?: string;
  status?: number;
  tenant?: number;
  page?: number;
}

export interface OnlineReportSaveDTO {
  reportId?: string;
  name: string;
  code: string;
  sqlContent: string;
  datasourceId: string;
  status: number;
  remark?: string;
  params?: OnlineReportParamDTO[];
  columns?: OnlineReportColumnDTO[];
  tenant?: number;
  page?: number;
}

export interface OnlineReportParamDTO {
  paramId?: string;
  reportId?: string;
  paramName: string;
  paramText: string;
  paramType: string;
  dictType?: string;
  defaultValue?: string;
  sort?: number;
}

export interface OnlineReportColumnDTO {
  columnId?: string;
  reportId?: string;
  columnName: string;
  columnText: string;
  columnLength: number;
  paramType: string;
  showFlag?: boolean;
  sort?: number;
  dictType?: string;
}

export interface ParseSqlDTO {
  dataSourceId: string;
  sql: string;
}

export interface ParseSqlVO {
  columns: OnlineReportColumnVO[];
  params: OnlineReportParamVO[];
}

export interface ExecuteReportDTO {
  pageNo: number;
  pageSize: number;
  [key: string]: any;
}

export interface ExecuteReportVO {
  list: Record<string, any>[];
  total: number;
}

export interface ReportParamValueDTO {
  paramName: string;
  paramValue: string;
}

export interface ReportDataQueryDTO {
  reportId: string;
  params?: ReportParamValueDTO[];
  pageNo?: number;
  pageSize?: number;
}

export interface ReportDataVO {
  rows: any[];
  total?: number;
}

// 获取在线报表分页
export function getOnlineReportPage(params: OnlineReportQueryDTO) {
  return requestClient.get<PageResult<OnlineReportVO>>(
    '/infrastructure/online-report/page',
    { params },
  );
}

// 获取在线报表详情
export function getOnlineReport(id: string) {
  return requestClient.get<OnlineReportVO>(
    '/infrastructure/online-report/get',
    { params: { id } },
  );
}

// 新增在线报表
export function createOnlineReport(data: OnlineReportSaveDTO) {
  return requestClient.post('/infrastructure/online-report/create', data);
}

// 修改在线报表
export function updateOnlineReport(data: OnlineReportSaveDTO) {
  return requestClient.put('/infrastructure/online-report/update', data);
}

// 删除在线报表
export function deleteOnlineReport(id: string) {
  return requestClient.delete('/infrastructure/online-report/delete', {
    params: { id },
  });
}

// 解析SQL语句
export function parseSql(data: ParseSqlDTO) {
  return requestClient.post('/infrastructure/online-report/parse-sql', data);
}

// 执行报表
export function executeReport(id: string, params: ExecuteReportDTO) {
  return requestClient.get<ExecuteReportVO>(
    '/infrastructure/online-report/execute',
    { params: { id, ...params } },
  );
}

// 导出报表
export function exportReport(id: string, params: Record<string, any>) {
  return requestClient.download('/infrastructure/online-report/export', {
    params: { id, ...params },
  });
}

// 查询报表数据
export function queryReportData(data: ReportDataQueryDTO) {
  return requestClient.post<ReportDataVO>(
    '/infrastructure/online-report/query-data',
    data,
  );
}
