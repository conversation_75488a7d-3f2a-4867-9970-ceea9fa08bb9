import type { PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

// ========== 类型定义 ==========

export interface OnlineTableVO {
  tableId: string;
  tableName: string;
  tableComment: string;
  sqlContent: string;
  tableType: number;
  privateKeyType: number;
  datasourceId: string;
  status: number;
  tenant: number;
  page: number;
  checkbox: number;
  scrollBar: number;
  tableFormStyle: number;
  version: number;
  columns: OnlineTableColumnVO[];
  pageConfigs: OnlineTablePageVO[];
  createTime?: number;
  updateTime?: number;
  creator?: string;
  updater?: string;
  deleted?: boolean;
}

export interface OnlineTableColumnVO {
  columnId: string;
  tableId: string;
  columnName: string;
  columnComment: string;
  columnType: string;
  columnLength: number;
  columnScale: number;
  isNullable: number;
  isPrimaryKey: number;
  isAutoIncrement: number;
  defaultValue: string;
  sort: number;
  showFlag: number;
  queryFlag: number;
  editFlag: number;
  listFlag: number;
  formFlag: number;
  componentType: string;
  componentDefaultValue: string;
  componentProps: string;
  dictType: string;
  validateRules: string;
  createTime?: number;
  updateTime?: number;
}

export interface OnlineTablePageVO {
  pageId: string;
  tableId: string;
  pageName: string;
  pageType: number;
  pageConfig: string;
  sort: number;
  createTime?: number;
  updateTime?: number;
}

export interface OnlineTableQueryDTO {
  pageNo: number;
  pageSize: number;
  tableName?: string;
  tableComment?: string;
  tableType?: number;
  datasourceId?: string;
  status?: number;
  tenant?: number;
}

export interface OnlineTableSaveDTO {
  tableId?: string;
  tableName: string;
  tableComment: string;
  sqlContent: string;
  tableType: number;
  privateKeyType: number;
  datasourceId: string;
  status: number;
  tenant: number;
  page: number;
  checkbox?: number;
  scrollBar?: number;
  tableFormStyle?: number;
  version?: number;
  columns: OnlineTableColumnDTO[];
  pageConfigs: OnlineTablePageDTO[];
}

export interface OnlineTableColumnDTO {
  columnId?: string;
  tableId?: string;
  columnName: string;
  columnComment: string;
  columnType: string;
  columnLength: number;
  columnScale: number;
  isNullable: number;
  isPrimaryKey: number;
  isAutoIncrement: number;
  defaultValue?: string;
  sort: number;
  showFlag: number;
  queryFlag: number;
  editFlag: number;
  listFlag: number;
  formFlag: number;
  componentType: string;
  componentDefaultValue?: string;
  componentProps?: string;
  dictType?: string;
  validateRules?: string;
}

export interface OnlineTablePageDTO {
  pageId?: string;
  tableId?: string;
  pageName: string;
  pageType: number;
  pageConfig: string;
  sort: number;
}

export interface OnlineTableSqlParseReqDTO {
  datasourceId: string;
  sqlContent: string;
}

export interface OnlineTableSqlParseRespVO {
  tableName: string;
  columns: OnlineTableColumnVO[];
}

// 数据操作相关类型
export interface OnlineTableDataQueryDTO {
  pageNo: number;
  pageSize: number;
  tableId: string;
  queryParams?: Record<string, any>;
}

export interface OnlineTableDataSaveDTO {
  tableId: string;
  dataId?: string;
  formData: Record<string, any>;
}

export interface OnlineTableDataVO {
  list: Record<string, any>[];
  total: number;
}

export interface OnlineTableDataImportRespVO {
  successCount: number;
  failureCount: number;
  failureList: Record<string, any>[];
  errorMessages: string[];
}

// ========== 配置管理API接口 ==========

// 获取在线表单分页
export function getOnlineTablePage(params: OnlineTableQueryDTO) {
  return requestClient.get<PageResult<OnlineTableVO>>(
    '/infrastructure/online-table/config/page',
    { params },
  );
}

// 获取在线表单详情
export function getOnlineTable(tableId: string) {
  return requestClient.get<OnlineTableVO>(
    '/infrastructure/online-table/config/get',
    { params: { tableId } },
  );
}

// 新增在线表单
export function createOnlineTable(data: OnlineTableSaveDTO) {
  return requestClient.post<string>(
    '/infrastructure/online-table/config/create',
    data,
  );
}

// 修改在线表单
export function updateOnlineTable(data: OnlineTableSaveDTO) {
  return requestClient.put<boolean>(
    '/infrastructure/online-table/config/update',
    data,
  );
}

// 删除在线表单
export function deleteOnlineTable(tableId: string) {
  return requestClient.delete<boolean>(
    '/infrastructure/online-table/config/delete',
    { params: { tableId } },
  );
}

// 复制在线表单
export function copyOnlineTable(tableId: string) {
  return requestClient.post<string>(
    `/infrastructure/online-table/config/copy/${tableId}`,
  );
}

// 同步数据库
export function syncOnlineTable(tableId: string) {
  return requestClient.post<boolean>(
    `/infrastructure/online-table/config/sync/${tableId}`,
  );
}

// 解析SQL获取表结构
export function parseSqlToTableStructure(data: OnlineTableSqlParseReqDTO) {
  return requestClient.post<OnlineTableSqlParseRespVO>(
    '/infrastructure/online-table/config/parse-sql',
    data,
  );
}

// 查询表单历史版本
export function getHistoryVersions(tableName: string) {
  return requestClient.get<OnlineTableVO[]>(
    `/infrastructure/online-table/config/history/${tableName}`,
  );
}

// ========== 数据管理API接口 ==========

// 查询在线表单数据
export function queryTableData(params: OnlineTableDataQueryDTO) {
  return requestClient.post<OnlineTableDataVO>(
    '/infrastructure/online-table/data/query',
    params,
  );
}

// 新增在线表单数据
export function createTableData(data: OnlineTableDataSaveDTO) {
  return requestClient.post<string>(
    '/infrastructure/online-table/data/create',
    data,
  );
}

// 修改在线表单数据
export function updateTableData(data: OnlineTableDataSaveDTO) {
  return requestClient.put<boolean>(
    '/infrastructure/online-table/data/update',
    data,
  );
}

// 删除在线表单数据
export function deleteTableData(tableId: string, dataId: string) {
  return requestClient.delete<boolean>(
    '/infrastructure/online-table/data/delete',
    { params: { tableId, dataId } },
  );
}

// 导出在线表单数据
export function exportTableData(tableId: string, params?: Record<string, any>) {
  return requestClient.download('/infrastructure/online-table/data/export', {
    params: { tableId, ...params },
  });
}

// 导入在线表单数据
export function importTableData(tableId: string, file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<OnlineTableDataImportRespVO>(
    `/infrastructure/online-table/data/import/${tableId}`,
    formData,
  );
}

// 下载在线表单导入模板
export function downloadImportTemplate(tableId: string) {
  return requestClient.download(
    `/infrastructure/online-table/data/import-template/${tableId}`,
  );
}
