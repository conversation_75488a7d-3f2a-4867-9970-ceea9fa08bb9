import { requestClient } from '#/api/request';

// ========== 在线表单配置管理接口 ==========

// 查询参数
export interface OnlineTableQueryDTO {
  pageNo: number;
  pageSize: number;
  tableName?: string;
  tableComment?: string;
  tableType?: number;
  datasourceId?: string;
  status?: number;
  tenant?: number;
}

// 保存参数
export interface OnlineTableSaveDTO {
  tableId?: string;
  tableName: string;
  tableComment: string;
  sqlContent: string;
  tableType: number;
  privateKeyType: number;
  datasourceId: string;
  status: number;
  tenant: number;
  page: number;
  checkbox?: number;
  scrollBar?: number;
  tableFormStyle?: number;
  version?: number; // 版本号
  columns: OnlineTableColumnDTO[];
  pageConfigs: OnlineTablePageDTO[];
}

// 字段配置DTO
export interface OnlineTableColumnDTO {
  columnId?: string;
  columnName: string;
  columnComment?: string;
  columnType: string;
  columnLength?: number;
  nullable: boolean;
  privateKey: boolean;
  syncDb: boolean;
  dictType?: string;
  isSystem?: boolean; // 是否为系统字段
}

// 页面配置DTO
export interface OnlineTablePageDTO {
  pageId?: string;
  columnId?: string;
  columnName: string;
  columnText?: string;
  componentType?: string;
  queryType?: string;
  validateRule?: string;
  labelLength?: number;
  componentLength?: number;
  componentDefaultValue?: string;
  sort?: number;
  queryFlag: boolean;
  formShowFlag: boolean;
  listShowFlag: boolean;
  readonlyFlag: boolean;
  sortFlag: boolean;
  sortType?: string;
  requiredFlag: boolean;
  dictType?: string;
}

// 响应对象
export interface OnlineTableVO {
  tableId: string;
  tableName: string;
  tableComment: string;
  sqlContent: string;
  tableType: number;
  privateKeyType: number;
  datasourceId: string;
  status: number;
  tenant: number;
  page: number;
  checkbox: number;
  scrollBar: number;
  tableFormStyle: number;
  version: number; // 版本号
  columns: OnlineTableColumnVO[];
  pageConfigs: OnlineTablePageVO[];
  createTime?: number;
  updateTime?: number;
  creator?: string;
  updater?: string;
  deleted?: boolean;
}

// 字段配置VO
export interface OnlineTableColumnVO {
  columnId: string;
  tableId: string;
  columnName: string;
  columnComment: string;
  columnType: string;
  columnLength: number;
  nullable: boolean;
  privateKey: boolean;
  syncDb: boolean;
  dictType: string;
  createTime?: number;
  updateTime?: number;
  creator?: string;
  updater?: string;
  deleted?: boolean;
}

// 页面配置VO
export interface OnlineTablePageVO {
  pageId: string;
  tableId: string;
  columnId: string;
  columnName: string;
  columnText: string;
  componentType: string;
  queryType: string;
  validateRule: string;
  labelLength: number;
  componentLength: number;
  componentDefaultValue: string;
  sort: number;
  queryFlag: boolean;
  formShowFlag: boolean;
  listShowFlag: boolean;
  readonlyFlag: boolean;
  sortFlag: boolean;
  sortType: string;
  requiredFlag: boolean;
  dictType: string;
  createTime?: number;
  updateTime?: number;
  creator?: string;
  updater?: string;
  deleted?: boolean;
}

// SQL解析请求DTO
export interface OnlineTableSqlParseReqDTO {
  datasourceId: string;
  sqlContent: string;
}

// SQL解析响应VO
export interface OnlineTableSqlParseRespVO {
  tableName: string;
  columns: OnlineTableColumnVO[];
}

// ========== 配置管理API接口 ==========

// 获取在线表单分页
export const getOnlineTablePage = (params: OnlineTableQueryDTO) => {
  return requestClient.get({
    url: '/infrastructure/online-table/config/page',
    params,
  });
};

// 获取在线表单详情
export const getOnlineTable = (tableId: string) => {
  return requestClient.get<OnlineTableVO>({
    url: '/infrastructure/online-table/config/get',
    params: { tableId },
  });
};

// 新增在线表单
export const createOnlineTable = (data: OnlineTableSaveDTO) => {
  return requestClient.post<string>({
    url: '/infrastructure/online-table/config/create',
    data,
  });
};

// 修改在线表单
export const updateOnlineTable = (data: OnlineTableSaveDTO) => {
  return requestClient.put<boolean>({
    url: '/infrastructure/online-table/config/update',
    data,
  });
};

// 删除在线表单
export const deleteOnlineTable = (tableId: string) => {
  return requestClient.delete<boolean>({
    url: '/infrastructure/online-table/config/delete',
    params: { tableId },
  });
};

// 复制在线表单
export const copyOnlineTable = (tableId: number) => {
  return requestClient.post<number>({
    url: `/infrastructure/online-table/config/copy/${tableId}`,
  });
};

// 同步数据库
export const syncOnlineTable = (tableId: number) => {
  return requestClient.post<boolean>({
    url: `/infrastructure/online-table/config/sync/${tableId}`,
  });
};

// 解析SQL获取表结构
export const parseSqlToTableStructure = (data: OnlineTableSqlParseReqDTO) => {
  return requestClient.post<OnlineTableSqlParseRespVO>({
    url: '/infrastructure/online-table/config/parse-sql',
    data,
  });
};

// 查询表单历史版本
export const getHistoryVersions = (tableName: string) => {
  return requestClient.get<OnlineTableVO[]>({
    url: `/infrastructure/online-table/config/history/${tableName}`,
  });
};

// ========== 兼容接口 ==========

// 兼容旧的SQL解析接口
export const parseSql = (data: { datasourceId: string; sql: string }) => {
  return parseSqlToTableStructure({
    datasourceId: data.datasourceId,
    sqlContent: data.sql,
  });
};
