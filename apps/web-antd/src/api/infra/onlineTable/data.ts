import { requestClient } from '#/api/request';

// ========== 动态表数据操作接口 ==========

// 查询参数
export interface OnlineTableDataQueryDTO {
  pageNo: number; // 页码，从 1 开始
  pageSize: number; // 每页条数，最大值为 100
  tableId: number; // 表格ID
  queryParams?: Record<string, any>; // 查询参数
}

// 保存参数
export interface OnlineTableDataSaveDTO {
  tableId: number; // 表格ID
  dataId?: number; // 数据ID（更新时必填）
  formData: Record<string, any>; // 表单数据
}

// 删除参数
export interface OnlineTableDataDeleteDTO {
  tableId: number; // 表格ID
  dataId: number; // 数据ID
}

// 查询响应
export interface OnlineTableDataVO {
  list: Record<string, any>[]; // 数据列表
  total: number; // 总数
}

// 导入结果响应
export interface OnlineTableDataImportRespVO {
  successCount: number; // 成功数量
  failureCount: number; // 失败数量
  failureList: Record<string, any>[]; // 失败数据列表
  errorMessages: string[]; // 错误信息列表
}

// ========== 数据管理API接口 ==========

// 查询在线表单数据
export const queryTableData = (params: OnlineTableDataQueryDTO) => {
  return requestClient.post<OnlineTableDataVO>({
    url: '/infrastructure/online-table/data/query',
    data: params,
  });
};

// 新增在线表单数据
export const createTableData = (data: OnlineTableDataSaveDTO) => {
  return requestClient.post<string>({
    url: '/infrastructure/online-table/data/create',
    data,
  });
};

// 修改在线表单数据
export const updateTableData = (data: OnlineTableDataSaveDTO) => {
  return requestClient.put<boolean>({
    url: '/infrastructure/online-table/data/update',
    data,
  });
};

// 删除在线表单数据
export const deleteTableData = (data: OnlineTableDataDeleteDTO) => {
  return requestClient.delete<boolean>({
    url: '/infrastructure/online-table/data/delete',
    data,
  });
};

// 导出在线表单数据
export const exportTableData = (params: OnlineTableDataQueryDTO) => {
  return requestClient.download({
    url: '/infrastructure/online-table/data/export',
    data: params,
  });
};

// 导入在线表单数据
export const importTableData = (tableId: number, file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<OnlineTableDataImportRespVO>({
    url: `/infrastructure/online-table/data/import/${tableId}`,
    data: formData,
  });
};

// 下载在线表单导入模板
export const downloadImportTemplate = (tableId: number) => {
  return requestClient.download({
    url: `/infrastructure/online-table/data/import-template/${tableId}`,
  });
};
