import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/infra/job/job-log',
    component: () => import('#/views/infra/job/logger/index.vue'),
    name: 'InfraJobLog',
    meta: {
      title: '调度日志',
      icon: 'ant-design:history-outlined',
      activePath: '/infra/job',
      keepAlive: false,
      hideInMenu: true,
    },
  },
  {
    path: '/codegen',
    name: 'CodegenEdit',
    meta: {
      title: '代码生成',
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      hideInMenu: true,
    },
    children: [
      {
        path: '/codegen/edit',
        name: 'InfraCodegenEdit',
        component: () => import('#/views/infra/codegen/edit/index.vue'),
        meta: {
          title: '修改生成配置',
          activeMenu: '/infra/codegen',
        },
      },
    ],
  },
  // 在线报表预览路由
  {
    path: '/common/online-report/preview/:id',
    name: 'OnlineReportPreview',
    component: () => import('#/views/common/onlineReport/preview.vue'),
    meta: {
      title: '报表预览',
      icon: 'ant-design:bar-chart-outlined',
      activePath: '/infra/online-report',
      hideInMenu: true,
    },
    props: (route) => ({
      reportId: route.params.id,
    }),
  },
  // 在线表单预览路由
  {
    path: '/common/online-table/preview/:id',
    name: 'OnlineTablePreview',
    component: () => import('#/views/common/onlineTable/preview.vue'),
    meta: {
      title: '表单预览',
      icon: 'ant-design:form-outlined',
      activePath: '/infra/online-table',
      hideInMenu: true,
    },
    props: (route) => ({
      tableId: route.params.id,
    }),
  },
];

export default routes;
