import type { UseWebSocketReturn } from '@vueuse/core';

import { computed, ref, watch } from 'vue';

import { useAccessStore, useUserStore } from '@vben/stores';

import { useWebSocket } from '@vueuse/core';
import { message } from 'ant-design-vue';

/**
 * WebSocket服务封装
 * 提供全局可复用的WebSocket连接服务
 */
export class WebSocketService {
  private static instance: null | WebSocketService = null;
  public data = ref('');
  public status = ref('CLOSED');
  public getIsOpen = computed(() => this.status.value === 'OPEN');
  // 连接状态
  public server = ref('');
  private connecting = false;

  private connectionAttempts = 0;
  private initialized = false;
  private maxConnectionAttempts = 3;
  // 消息处理器集合
  private messageHandlers: Map<string, (content: any) => void> = new Map();

  private webSocketInstance: null | UseWebSocketReturn<unknown> = null;
  private reconnectTimer: number | null = null;

  private constructor() {
    // 私有构造函数，防止外部直接实例化
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * 连接WebSocket
   */
  public connect(): void {
    if (!this.initialized) {
      console.warn('[WebSocketService] 尚未初始化，请先调用init()方法');
      return;
    }

    // 如果正在连接中，不重复连接
    if (this.connecting) {
      console.log('[WebSocketService] 正在连接中，请勿重复调用');
      return;
    }

    // 如果已经连接，不重复连接
    if (this.webSocketInstance && this.getIsOpen.value) {
      console.log('[WebSocketService] 已经连接，无需重复连接');
      return;
    }

    // 如果超过最大尝试次数，不再尝试
    if (this.connectionAttempts >= this.maxConnectionAttempts) {
      console.log(
        `[WebSocketService] 已达到最大连接尝试次数(${this.maxConnectionAttempts})，不再尝试连接`,
      );
      message.error('WebSocket连接失败，请刷新页面重试');
      return;
    }

    try {
      this.connecting = true;
      this.connectionAttempts++;

      const accessStore = useAccessStore();
      const accessToken = accessStore.accessToken as string;

      if (!accessToken) {
        console.warn('[WebSocketService] 未找到访问令牌，无法连接');
        this.connecting = false;
        return;
      }

      // 设置服务器地址
      this.server.value = `${`${import.meta.env.VITE_BASE_URL}/admin-api/system/ws`.replace(
        'http',
        'ws',
      )}?token=${accessToken}`;

      console.log(
        `[WebSocketService] 开始创建连接(尝试 ${this.connectionAttempts}/${this.maxConnectionAttempts})...`,
      );

      // 创建WebSocket连接
      const wsInstance = useWebSocket(this.server.value, {
        autoReconnect: false, // 禁用自动重连，由我们自己控制
        heartbeat: {
          message: 'ping',
          interval: 10_000, // 10秒发送一次心跳
        },
        immediate: true, // 立即连接
        onMessage: (ws, event) => {
          this.handleMessage(event.data);
        },
        onConnected: (ws: WebSocket) => {
          console.log('[WebSocketService] 连接成功');
          this.connecting = false;
          this.connectionAttempts = 0; // 连接成功后重置尝试次数
          
          // 清除重连定时器
          if (this.reconnectTimer !== null) {
            window.clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
          }
        },
        onDisconnected: (ws: WebSocket) => {
          console.log('[WebSocketService] 连接断开');
          this.connecting = false;
          
          // 如果用户仍然登录，尝试重连
          const userStore = useUserStore();
          if (userStore.userInfo) {
            this.scheduleReconnect();
          }
        },
        onError: (ws: WebSocket, event: Event) => {
          console.error('[WebSocketService] 连接错误:', event);
          this.connecting = false;
          
          // 如果用户仍然登录，尝试重连
          const userStore = useUserStore();
          if (userStore.userInfo) {
            this.scheduleReconnect();
          }
        },
      });

      // 保存实例
      this.webSocketInstance = wsInstance;
      this.status = wsInstance.status;
      this.data = wsInstance.data;
    } catch (error) {
      console.error('[WebSocketService] 创建连接失败:', error);
      this.connecting = false;
      
      // 如果用户仍然登录，尝试重连
      const userStore = useUserStore();
      if (userStore.userInfo) {
        this.scheduleReconnect();
      }
    }
  }

  /**
   * 安排重新连接
   */
  private scheduleReconnect(): void {
    // 避免多个重连定时器
    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
    }
    
    // 设置递增的重连延迟，从1秒到10秒
    const delay = Math.min(1000 * Math.pow(2, this.connectionAttempts - 1), 10000);
    console.log(`[WebSocketService] 计划在 ${delay}ms 后重新连接`);
    
    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectTimer = null;
      this.connect();
    }, delay);
  }

  /**
   * 断开WebSocket连接
   */
  public disconnect(): void {
    // 清除重连定时器
    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (!this.webSocketInstance) {
      return;
    }

    try {
      console.log('[WebSocketService] 断开连接');
      this.webSocketInstance.close();
      this.webSocketInstance = null;
      this.status.value = 'CLOSED';
      this.connectionAttempts = 0; // 重置连接尝试次数
    } catch (error) {
      console.error('[WebSocketService] 断开连接失败:', error);
    }
  }

  /**
   * 初始化WebSocket服务
   * 应在应用初始化后调用，确保Pinia已经准备好
   */
  public init(): void {
    if (this.initialized) {
      console.log('[WebSocketService] 已经初始化，跳过');
      return;
    }

    try {
      console.log('[WebSocketService] 开始初始化...');

      // 标记为已初始化
      this.initialized = true;

      // 设置用户登录状态监听
      this.setupUserLoginWatch();

      console.log('[WebSocketService] 初始化成功');
    } catch (error) {
      console.error('[WebSocketService] 初始化失败:', error);
      this.initialized = false;
    }
  }

  /**
   * 注册消息处理器
   * @param type 消息类型
   * @param handler 处理函数
   */
  public registerMessageHandler(
    type: string,
    handler: (content: any) => void,
  ): void {
    this.messageHandlers.set(type, handler);
  }

  /**
   * 移除消息处理器
   * @param type 消息类型
   */
  public removeMessageHandler(type: string): void {
    this.messageHandlers.delete(type);
  }

  /**
   * 发送消息
   * @param type 消息类型
   * @param content 消息内容
   */
  public sendMessage(type: string, content: any): void {
    if (!this.webSocketInstance || !this.getIsOpen.value) {
      message.warning('WebSocket未连接，请先连接');
      return;
    }

    // 构造消息
    const messageContent = JSON.stringify(content);
    const jsonMessage = JSON.stringify({
      type,
      content: messageContent,
    });

    // 发送消息
    this.webSocketInstance.send(jsonMessage);
  }

  /**
   * 处理接收到的消息
   * @param messageData 消息数据
   */
  private handleMessage(messageData: string): void {
    // 心跳响应
    if (messageData === 'pong') {
      return;
    }

    try {
      // 解析消息
      const jsonMessage = JSON.parse(messageData);
      const type = jsonMessage.type;
      const content = JSON.parse(jsonMessage.content);

      // 分发消息到对应的处理器
      if (this.messageHandlers.has(type)) {
        this.messageHandlers.get(type)!(content);
      } else {
        console.warn(`[WebSocketService] 未处理的消息类型: ${type}`);
      }
    } catch (error) {
      console.error('[WebSocketService] 处理消息发生异常:', error);
    }
  }

  /**
   * 设置用户登录状态监听
   */
  private setupUserLoginWatch(): void {
    const userStore = useUserStore();
    const accessStore = useAccessStore();

    // 初始检查 - 如果用户已登录，则连接
    if (userStore.userInfo && accessStore.accessToken) {
      console.log('[WebSocketService] 用户已登录，准备连接');
      this.connect();
    }

    // 使用watch监听用户信息变化
    watch(
      () => userStore.userInfo,
      (newUserInfo, oldUserInfo) => {
        console.log(
          '[WebSocketService] 用户信息变化',
          oldUserInfo ? '已登录' : '未登录',
          '->',
          newUserInfo ? '已登录' : '未登录',
        );

        if (newUserInfo && !oldUserInfo) {
          // 用户登录
          console.log('[WebSocketService] 用户登录，创建连接');
          this.connect();
        } else if (!newUserInfo && oldUserInfo) {
          // 用户登出
          console.log('[WebSocketService] 用户登出，断开连接');
          this.disconnect();
        }
      },
    );
    
    // 监听访问令牌变化
    watch(
      () => accessStore.accessToken,
      (newToken, oldToken) => {
        console.log('[WebSocketService] 访问令牌变化');
        
        // 如果令牌变化且用户已登录，重新连接
        if (newToken && newToken !== oldToken && userStore.userInfo) {
          console.log('[WebSocketService] 访问令牌更新，重新连接');
          // 先断开旧连接
          if (this.webSocketInstance) {
            this.disconnect();
          }
          // 使用新令牌连接
          this.connect();
        }
      }
    );
  }
}

// 导出单例实例，但不立即初始化
export const webSocketService = WebSocketService.getInstance();
