# 管理后台


**简介**:管理后台


**HOST**:http://************:48089/demo


**联系人**:


**Version**:1.0.0


**接口路径**:/demo/v3/api-docs/all


[TOC]






# 管理后台 - 订单管理


## 更新订单


**接口地址**:`/demo/orders/update`


**请求方式**:`PUT`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 1,
  "status": 1,
  "payAmount": 100,
  "payType": 1,
  "payTime": 1718352000000,
  "remark": "测试订单"
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ordersUpdateDTO|管理后台 - 订单更新 DTO|body|true|OrdersUpdateDTO|OrdersUpdateDTO|
|&emsp;&emsp;id|订单ID||true|integer(int64)||
|&emsp;&emsp;status|订单状态||false|integer(int32)||
|&emsp;&emsp;payAmount|实付金额||false|number||
|&emsp;&emsp;payType|支付方式||false|integer(int32)||
|&emsp;&emsp;payTime|支付时间||false|integer(int64)||
|&emsp;&emsp;remark|订单备注||false|string||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultBoolean|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||boolean||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```


## 支付订单


**接口地址**:`/demo/orders/pay`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(int64)||
|payAmount||query|true|number||
|payType||query|true|integer(int32)||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultBoolean|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||boolean||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```


## 获得订单分页


**接口地址**:`/demo/orders/page`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "pageNo": 1,
  "pageSize": 10,
  "orderNo": "ORD202506140001",
  "userId": 1,
  "status": 1,
  "payType": 1,
  "minTotalAmount": 100,
  "maxTotalAmount": 1000,
  "createTime": [],
  "payTime": []
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ordersQueryDTO|管理后台 - 订单分页查询 DTO|body|true|OrdersQueryDTO|OrdersQueryDTO|
|&emsp;&emsp;pageNo|页码，从 1 开始||true|integer(int32)||
|&emsp;&emsp;pageSize|每页条数，最大值为 100||true|integer(int32)||
|&emsp;&emsp;orderNo|订单编号||false|string||
|&emsp;&emsp;userId|下单用户ID||false|integer(int64)||
|&emsp;&emsp;status|订单状态||false|integer(int32)||
|&emsp;&emsp;payType|支付方式||false|integer(int32)||
|&emsp;&emsp;minTotalAmount|订单总金额最小值||false|number||
|&emsp;&emsp;maxTotalAmount|订单总金额最大值||false|number||
|&emsp;&emsp;createTime|创建时间||false|array|integer(int64)|
|&emsp;&emsp;payTime|支付时间||false|array|integer(int64)|
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultPageResultOrdersVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||PageResultOrdersVO|PageResultOrdersVO|
|&emsp;&emsp;list|数据|array|OrdersVO|
|&emsp;&emsp;&emsp;&emsp;id|订单ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;orderNo|订单编号|string||
|&emsp;&emsp;&emsp;&emsp;userId|下单用户ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;status|订单状态|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;totalAmount|订单总金额|number||
|&emsp;&emsp;&emsp;&emsp;payAmount|实付金额|number||
|&emsp;&emsp;&emsp;&emsp;payType|支付方式|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;payTime|支付时间|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;updateTime|更新时间|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;remark|订单备注|string||
|&emsp;&emsp;total|总量|integer(int64)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1,
				"orderNo": "ORD202506140001",
				"userId": 1,
				"status": 1,
				"totalAmount": 100,
				"payAmount": 100,
				"payType": 1,
				"payTime": 1718352000000,
				"createTime": 1718352000000,
				"updateTime": 1718352000000,
				"remark": "测试订单"
			}
		],
		"total": 0
	},
	"msg": ""
}
```


## 导入订单


**接口地址**:`/demo/orders/import`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|file|Excel 文件|query|true|||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultString|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||string||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": "",
	"msg": ""
}
```


## 导出订单Excel


**接口地址**:`/demo/orders/export`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "pageNo": 1,
  "pageSize": 10,
  "orderNo": "ORD202506140001",
  "userId": 1,
  "status": 1,
  "payType": 1,
  "minTotalAmount": 100,
  "maxTotalAmount": 1000,
  "createTime": [],
  "payTime": []
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ordersExportDTO|管理后台 - 订单导出 DTO|body|true|OrdersExportDTO|OrdersExportDTO|
|&emsp;&emsp;pageNo|页码，从 1 开始||true|integer(int32)||
|&emsp;&emsp;pageSize|每页条数，最大值为 100||true|integer(int32)||
|&emsp;&emsp;orderNo|订单编号||false|string||
|&emsp;&emsp;userId|下单用户ID||false|integer(int64)||
|&emsp;&emsp;status|订单状态||false|integer(int32)||
|&emsp;&emsp;payType|支付方式||false|integer(int32)||
|&emsp;&emsp;minTotalAmount|订单总金额最小值||false|number||
|&emsp;&emsp;maxTotalAmount|订单总金额最大值||false|number||
|&emsp;&emsp;createTime|创建时间||false|array|integer(int64)|
|&emsp;&emsp;payTime|支付时间||false|array|integer(int64)|
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```


## 创建订单


**接口地址**:`/demo/orders/create`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "orderNo": "ORD202506140001",
  "userId": 1,
  "totalAmount": 100,
  "remark": "测试订单"
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ordersAddDTO|管理后台 - 订单新增 DTO|body|true|OrdersAddDTO|OrdersAddDTO|
|&emsp;&emsp;orderNo|订单编号||true|string||
|&emsp;&emsp;userId|下单用户ID||true|integer(int64)||
|&emsp;&emsp;totalAmount|订单总金额||true|number||
|&emsp;&emsp;remark|订单备注||false|string||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultLong|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||integer(int64)|integer(int64)|
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```


## 取消订单


**接口地址**:`/demo/orders/cancel`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|订单ID|query|true|integer(int64)||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultBoolean|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||boolean||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```


## 获得订单


**接口地址**:`/demo/orders/get`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|订单ID|query|true|integer(int64)||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultOrdersVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||OrdersVO|OrdersVO|
|&emsp;&emsp;id|订单ID|integer(int64)||
|&emsp;&emsp;orderNo|订单编号|string||
|&emsp;&emsp;userId|下单用户ID|integer(int64)||
|&emsp;&emsp;status|订单状态|integer(int32)||
|&emsp;&emsp;totalAmount|订单总金额|number||
|&emsp;&emsp;payAmount|实付金额|number||
|&emsp;&emsp;payType|支付方式|integer(int32)||
|&emsp;&emsp;payTime|支付时间|integer(int64)||
|&emsp;&emsp;createTime|创建时间|integer(int64)||
|&emsp;&emsp;updateTime|更新时间|integer(int64)||
|&emsp;&emsp;remark|订单备注|string||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"id": 1,
		"orderNo": "ORD202506140001",
		"userId": 1,
		"status": 1,
		"totalAmount": 100,
		"payAmount": 100,
		"payType": 1,
		"payTime": 1718352000000,
		"createTime": 1718352000000,
		"updateTime": 1718352000000,
		"remark": "测试订单"
	},
	"msg": ""
}
```


## 获得导入订单模板


**接口地址**:`/demo/orders/get-import-template`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```


## 删除订单


**接口地址**:`/demo/orders/delete`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|订单ID|query|true|integer(int64)||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultBoolean|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||boolean||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```


# trans-proxy-controller


## findByIds


**接口地址**:`/demo/easyTrans/proxy/{targetClass}/findByIds`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "ids": [],
  "uniqueField": "",
  "targetFields": []
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|targetClass||path|true|string||
|findByIdsQueryPayload|FindByIdsQueryPayload|body|true|FindByIdsQueryPayload|FindByIdsQueryPayload|
|&emsp;&emsp;ids|||false|array|string|
|&emsp;&emsp;uniqueField|||false|string||
|&emsp;&emsp;targetFields|||false|array|string|
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```


## findById


**接口地址**:`/demo/easyTrans/proxy/{targetClass}/findById/{id}`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|targetClass||path|true|string||
|id||path|true|string||
|uniqueField||query|true|string||
|targetFields||query|true|string||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```


# 管理后台 - OA 请假申请


## updateLeaveStatus


**接口地址**:`/demo/bpm/oa/leave/updateLeaveStatus`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(int64)||
|status||query|true|integer(int32)||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultVoid|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||object||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {},
	"msg": ""
}
```


## 创建请求申请


**接口地址**:`/demo/bpm/oa/leave/create`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "startTime": 0,
  "endTime": 0,
  "type": 1,
  "reason": "阅读",
  "startUserSelectAssignees": "{taskKey1: [1, 2]}",
  "endTimeValid": true
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|bpmOALeaveCreateReqVO|管理后台 - 请假申请创建 Request VO|body|true|BpmOALeaveCreateReqVO|BpmOALeaveCreateReqVO|
|&emsp;&emsp;startTime|请假的开始时间||true|integer(int64)||
|&emsp;&emsp;endTime|请假的结束时间||true|integer(int64)||
|&emsp;&emsp;type|请假类型-参见 bpm_oa_type 枚举||true|integer(int32)||
|&emsp;&emsp;reason|原因||true|string||
|&emsp;&emsp;startUserSelectAssignees|发起人自选审批人 Map||false|object||
|&emsp;&emsp;endTimeValid|||false|boolean||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultLong|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||integer(int64)|integer(int64)|
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```


## 获得请假申请分页


**接口地址**:`/demo/bpm/oa/leave/page`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageVO|管理后台 - 请假申请分页 Request VO|query|true|BpmOALeavePageReqVO|BpmOALeavePageReqVO|
|&emsp;&emsp;pageNo|页码，从 1 开始||true|integer(int32)||
|&emsp;&emsp;pageSize|每页条数，最大值为 100||true|integer(int32)||
|&emsp;&emsp;status|状态||false|integer(int32)||
|&emsp;&emsp;type|请假类型，参见 bpm_oa_type||false|integer(int32)||
|&emsp;&emsp;reason|原因，模糊匹配||false|string||
|&emsp;&emsp;createTime|申请时间||false|array|integer(int64)|
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultPageResultBpmOALeaveRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||PageResultBpmOALeaveRespVO|PageResultBpmOALeaveRespVO|
|&emsp;&emsp;list|数据|array|BpmOALeaveRespVO|
|&emsp;&emsp;&emsp;&emsp;id|请假表单主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;type|请假类型，参见 bpm_oa_type 枚举|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;reason|原因|string||
|&emsp;&emsp;&emsp;&emsp;createTime|申请时间|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;startTime|请假的开始时间|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;endTime|请假的结束时间|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;processInstanceId|流程编号|string||
|&emsp;&emsp;&emsp;&emsp;status|审批结果|integer(int32)||
|&emsp;&emsp;total|总量|integer(int64)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 1024,
				"type": 1,
				"reason": "阅读XXXX",
				"createTime": 0,
				"startTime": 0,
				"endTime": 0,
				"processInstanceId": "",
				"status": 1
			}
		],
		"total": 0
	},
	"msg": ""
}
```


## 获得请假申请


**接口地址**:`/demo/bpm/oa/leave/get`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|编号|query|true|integer(int64)||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultBpmOALeaveRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||BpmOALeaveRespVO|BpmOALeaveRespVO|
|&emsp;&emsp;id|请假表单主键|integer(int64)||
|&emsp;&emsp;type|请假类型，参见 bpm_oa_type 枚举|integer(int32)||
|&emsp;&emsp;reason|原因|string||
|&emsp;&emsp;createTime|申请时间|integer(int64)||
|&emsp;&emsp;startTime|请假的开始时间|integer(int64)||
|&emsp;&emsp;endTime|请假的结束时间|integer(int64)||
|&emsp;&emsp;processInstanceId|流程编号|string||
|&emsp;&emsp;status|审批结果|integer(int32)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"id": 1024,
		"type": 1,
		"reason": "阅读XXXX",
		"createTime": 0,
		"startTime": 0,
		"endTime": 0,
		"processInstanceId": "",
		"status": 1
	},
	"msg": ""
}
```


# version-controller


## ping


**接口地址**:`/demo/ping`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```


## getVersion


**接口地址**:`/demo/get-version`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```


# git-info-controller


## getGitInfo


**接口地址**:`/demo/git-info`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```